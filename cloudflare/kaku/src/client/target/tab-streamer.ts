/**
 * Target Tab Streamer Script
 *
 * This script is injected into target tabs to automatically capture their content
 * and stream it via WebRTC. Uses preferCurrentTab: true for automatic capture.
 */

(() => {
  'use strict';

  // Prevent multiple injections
  if (window.TabStreamer) {
    console.log('[POC-Streamer] Target streamer already initialized');
    return;
  }

  class TabStreamer {
    // Connection properties
    private signalingServerUrl: string;
    private ws: WebSocket | null;
    private tabId: string;
    private isConnected: boolean;
    private captureStream: MediaStream | null;
    private controlTabPeerConnection: RTCPeerConnection | null;

    constructor(signalingServerUrl: string, tabId: string, autoInitialize: string) {
      this.signalingServerUrl = signalingServerUrl;
      this.ws = null;
      this.tabId = tabId;
      this.isConnected = false;
      this.captureStream = null;
      this.controlTabPeerConnection = null;

      console.log('[POC-Streamer] Target tab streamer initializing...');
      this.init(autoInitialize === 'true');
    }

    async init(autoInitialize: boolean): Promise<void> {
      try {
        // Tab ID is already set in constructor
        console.log('[POC-Streamer] Using tab ID:', this.tabId);

        // Connect to signaling server
        await this.connectToSignalingServer();

        if (autoInitialize) {
          await this.handleStartStream();
        }

        console.log('[POC-Streamer] Target tab streamer initialized successfully');
      } catch (error) {
        console.error('[POC-Streamer] Failed to initialize target streamer:', error);
      }
    }

    getTabId(): string {
      // Try to get tab ID from various sources
      const urlParams = new URLSearchParams(window.location.search);
      const tabId =
        urlParams.get('tabId') ||
        window.name ||
        document.title ||
        window.location.hostname ||
        this.generateId();

      console.log('[POC-Streamer] Tab ID:', tabId);
      return tabId;
    }

    async connectToSignalingServer(): Promise<void> {
      return new Promise<void>((resolve, reject) => {
        this.ws = new WebSocket(this.signalingServerUrl);

        this.ws.onopen = () => {
          console.log('[POC-Streamer] Connected to signaling server');
          this.isConnected = true;

          // Register as target tab
          this.sendMessage({
            type: 'register-target-tab',
            tabId: this.tabId,
            url: window.location.href,
            title: document.title,
            metadata: {
              userAgent: navigator.userAgent,
              timestamp: Date.now(),
            },
          });

          resolve();
        };

        this.ws.onmessage = (event: MessageEvent) => {
          try {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('[POC-Streamer] Failed to parse message:', error);
          }
        };

        this.ws.onclose = () => {
          console.log('[POC-Streamer] Disconnected from signaling server');
          this.isConnected = false;

          // Attempt to reconnect after delay
          setTimeout(() => {
            if (!this.isConnected) {
              console.log('[POC-Streamer] Attempting to reconnect...');
              this.connectToSignalingServer().catch(console.error);
            }
          }, 5000);
        };

        this.ws.onerror = (error: Event) => {
          console.error('[POC-Streamer] WebSocket error:', error);
          reject(error);
        };

        // Timeout for connection
        setTimeout(() => {
          if (!this.isConnected) {
            reject(new Error('Connection timeout'));
          }
        }, 10000);
      });
    }

    async handleMessage(message: any): Promise<void> {
      console.log('[POC-Streamer] Received message:', message.type);

      switch (message.type) {
        case 'start-streaming':
          await this.handleStartStream();
          break;

        case 'webrtc-answer-to-target':
          await this.handleWebRTCAnswer(message);
          break;

        case 'webrtc-ice-candidate-to-target':
          await this.handleICECandidate(message);
          break;
        case 'stop-streaming':
          // await this.handleStopStream(message);
          break;
        default:
          console.log('[POC-Streamer] Unhandled message type:', message.type);
      }
    }

    async handleStartStream(): Promise<void> {
      try {
        console.log('[POC-Streamer] Starting stream for:', this.tabId);

        // Capture current tab content automatically
        const stream = await this.captureCurrentTab();
        this.captureStream = stream;

        // Create peer connection for this stream
        const peerConnection = await this.createPeerConnection();

        // Add stream tracks to peer connection
        stream.getTracks().forEach((track: MediaStreamTrack) => {
          peerConnection.addTrack(track, stream);
        });

        // Create and send offer
        const offer = await peerConnection.createOffer();
        await peerConnection.setLocalDescription(offer);

        this.sendMessage({
          type: 'webrtc-offer-from-target',
          offer: offer,
          targetTabId: this.tabId,
        });

        // Notify server that streaming is ready
        this.sendMessage({
          type: 'streaming-ready',
          tabId: this.tabId,
        });

        console.log('[POC-Streamer] Stream started and offer sent');
      } catch (error) {
        console.error('[POC-Streamer] Failed to start stream:', error);

        // Send error back to signaling server
        this.sendMessage({
          type: 'stream-error',
          tabId: this.tabId,
          error: (error as Error).message,
        });
      }
    }

    async captureCurrentTab(): Promise<MediaStream> {
      try {
        console.log('[POC-Streamer] Capturing current tab content...');

        // Use getDisplayMedia with preferCurrentTab for automatic capture
        const stream = await navigator.mediaDevices.getDisplayMedia({
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            frameRate: { ideal: 100 },
          },
          audio: false, // Disable audio for simplicity
          // Note: preferCurrentTab is not a standard property but may work in some browsers
        } as DisplayMediaStreamOptions);

        console.log('[POC-Streamer] Tab content captured successfully');
        console.log('[POC-Streamer] Stream active:', stream.active);
        console.log('[POC-Streamer] Stream tracks:', stream.getTracks());

        // Check each track
        stream.getTracks().forEach((track: MediaStreamTrack, index: number) => {
          console.log(`[POC-Streamer] Track ${index}:`, {
            kind: track.kind,
            enabled: track.enabled,
            muted: track.muted,
            readyState: track.readyState,
            label: track.label,
          });
        });

        return stream;
      } catch (error) {
        console.error('[POC-Streamer] Failed to capture tab content:', error);

        throw error;
      }
    }

    async createPeerConnection(): Promise<RTCPeerConnection> {
      const peerConnection = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }],
      });

      // Store peer connection using tabId as key
      this.controlTabPeerConnection = peerConnection;

      // Handle ICE candidates
      peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          this.sendMessage({
            type: 'webrtc-ice-candidate-from-target',
            candidate: event.candidate,
            targetTabId: this.tabId,
          });
        }
      };

      // Handle connection state changes
      peerConnection.onconnectionstatechange = () => {
        console.log('[POC-Streamer] Connection state:', peerConnection.connectionState);
      };

      return peerConnection;
    }

    async handleWebRTCAnswer(message: any): Promise<void> {
      if (this.controlTabPeerConnection) {
        await this.controlTabPeerConnection.setRemoteDescription(message.answer);
        console.log('[POC-Streamer] WebRTC answer processed');
      }
    }

    async handleICECandidate(message: any): Promise<void> {
      if (this.controlTabPeerConnection) {
        await this.controlTabPeerConnection.addIceCandidate(message.candidate);
      }
    }

    sendMessage(message: any): void {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(message));
      }
    }

    generateId(): string {
      return Math.random().toString(36).substring(2, 11);
    }

    cleanup(): void {
      console.log('[POC-Streamer] Cleaning up target streamer...');

      // Stop capture stream
      if (this.captureStream) {
        this.captureStream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
      }

      // Close peer connections
      if (this.controlTabPeerConnection) {
        this.controlTabPeerConnection.close();
        this.controlTabPeerConnection = null;
      }

      if (this.ws) {
        this.ws.close();
      }
    }
  }

  // Initialize the target tab streamer
  const signalingServerUrl = '${SIGNALING_SERVER_URL}';
  const tabId = '${TAB_ID}';
  const autoInitialize = '${AUTO_INITIALIZE}';
  window.TabStreamer = new TabStreamer(signalingServerUrl, tabId, autoInitialize);

  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    if (window.TabStreamer) {
      window.TabStreamer.cleanup();
    }
  });
})();
