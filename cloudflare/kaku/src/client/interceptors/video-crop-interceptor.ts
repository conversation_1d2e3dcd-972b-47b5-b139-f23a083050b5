/**
 * Video Frame Interceptor
 *
 * A modular interceptor that sits between video track capture and WebRTC peer connection.
 * Provides frame cropping and subscription capabilities for real-time frame analysis.
 *
 * Features:
 * - Frame cropping with configurable crop regions
 * - Event-driven subscription API for frame listeners
 * - Toggle-able interceptor functionality
 * - Maintains existing streaming functionality
 * - Support for multiple frame subscribers
 * - Extends BaseInterceptor for standardized interface
 */

import BaseInterceptor from './base-interceptor.js';
import type { VideoCropConfig } from '../types';

interface CropRegion {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface SubscriberMetadata {
  subscriberId: string;
  frameCount: number;
  timestamp: number;
  cropRegion: CropRegion | null;
}

type FrameSubscriberCallback = (
  frame: VideoFrame,
  metadata: SubscriberMetadata,
) => void | Promise<void>;

class VideoFrameInterceptor extends BaseInterceptor {
  // Video-specific state management
  private subscribers = new Map<string, FrameSubscriberCallback>();
  private frameCount: number = 0;
  private lastFrameTime: number = 0;

  declare config: VideoCropConfig;

  constructor(name: string = 'video-crop', options: VideoCropConfig = {}) {
    // Default configuration for video cropping interceptor
    const defaultConfig: VideoCropConfig = {
      debug: false,
      frameRate: 30,
      enableCropping: true,
      ...options,
    };

    super(name, defaultConfig);

    this.log('VideoFrameInterceptor initialized', this.config);
  }

  /**
   * Process a video frame - implements BaseInterceptor interface
   */
  async processVideoFrame(frame: VideoFrame): Promise<VideoFrame> {
    this.frameCount++;
    const currentTime = performance.now();

    // Throttle debug logging
    if (this.config.debug && currentTime - this.lastFrameTime > 1000) {
      this.log(`Processed ${this.frameCount} frames`);
      this.lastFrameTime = currentTime;
    }

    let processedFrame = frame;

    // Apply cropping if enabled and crop region is set
    if (this.config.enableCropping && (this.config.cropRegion || this.config.defaultCropRegion)) {
      processedFrame = this.applyCropping(frame);
    }

    // Notify subscribers with the processed frame (before returning)
    if (this.subscribers.size > 0) {
      await this.notifySubscribers(processedFrame);
    }

    return processedFrame;
  }

  /**
   * Apply cropping to a video frame
   */
  private applyCropping(frame: VideoFrame): VideoFrame {
    const { codedWidth, codedHeight } = frame;
    const cropRegion = this.config.cropRegion || this.config.defaultCropRegion;

    if (!cropRegion) {
      return frame;
    }

    // Ensure crop region is within frame bounds and aligned for YUV 4:2:0
    const safeCropRegion: CropRegion = {
      x: this.makeEven(Math.max(0, Math.min(cropRegion.x, codedWidth))),
      y: this.makeEven(Math.max(0, Math.min(cropRegion.y, codedHeight))),
      width: this.makeEven(Math.max(2, Math.min(cropRegion.width, codedWidth - cropRegion.x))),
      height: this.makeEven(Math.max(2, Math.min(cropRegion.height, codedHeight - cropRegion.y))),
    };

    try {
      const croppedFrame = new VideoFrame(frame, {
        visibleRect: safeCropRegion,
        displayWidth: safeCropRegion.width,
        displayHeight: safeCropRegion.height,
        timestamp: frame.timestamp,
        duration: frame.duration ?? undefined,
      });

      this.log('Frame cropped:', safeCropRegion);
      return croppedFrame;
    } catch (error) {
      this.log('Cropping failed, using original frame:', error);
      return frame;
    }
  }

  /**
   * Notify all subscribers with the processed frame
   */
  private async notifySubscribers(frame: VideoFrame): Promise<void> {
    const promises: Promise<void>[] = [];

    for (const [subscriberId, callback] of this.subscribers) {
      try {
        // Clone frame for each subscriber to prevent conflicts
        const frameClone = new VideoFrame(frame, {
          timestamp: frame.timestamp,
          duration: frame.duration ?? undefined,
        });

        const metadata: SubscriberMetadata = {
          subscriberId,
          frameCount: this.frameCount,
          timestamp: performance.now(),
          cropRegion: this.config.cropRegion || this.config.defaultCropRegion || null,
        };

        const result = callback(frameClone, metadata);

        // Handle async callbacks
        if (result instanceof Promise) {
          promises.push(result);
        }
      } catch (error) {
        this.log(`Error notifying subscriber ${subscriberId}:`, error);
      }
    }

    // Wait for all async callbacks to complete
    if (promises.length > 0) {
      await Promise.allSettled(promises);
    }
  }

  /**
   * Subscribe to processed frames
   */
  subscribe(subscriberId: string, callback: FrameSubscriberCallback): () => boolean {
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    this.subscribers.set(subscriberId, callback);
    this.log(`Subscriber added: ${subscriberId} (total: ${this.subscribers.size})`);

    // Return unsubscribe function
    return () => this.unsubscribe(subscriberId);
  }

  /**
   * Unsubscribe from processed frames
   */
  unsubscribe(subscriberId: string): boolean {
    const removed = this.subscribers.delete(subscriberId);
    if (removed) {
      this.log(`Subscriber removed: ${subscriberId} (remaining: ${this.subscribers.size})`);
    }
    return removed;
  }

  /**
   * Update crop region
   */
  setCropRegion(cropRegion: Partial<CropRegion> | null): void {
    if (cropRegion && typeof cropRegion === 'object') {
      this.updateConfig({
        cropRegion: {
          x: this.makeEven(cropRegion.x || 0),
          y: this.makeEven(cropRegion.y || 0),
          width: this.makeEven(cropRegion.width || 100),
          height: this.makeEven(cropRegion.height || 100),
        },
      });
      this.log('Crop region updated:', this.config.cropRegion);
    } else {
      this.updateConfig({ cropRegion: null });
      this.log('Crop region cleared');
    }
  }

  /**
   * Enable or disable cropping functionality
   */
  setCroppingEnabled(enabled: boolean): void {
    this.updateConfig({
      enableCropping: !!enabled,
    });
    this.log(`Cropping ${this.config.enableCropping ? 'enabled' : 'disabled'}`);
  }

  /**
   * Get current interceptor status
   */
  getStatus(): {
    name: string;
    type: string;
    isInitialized: boolean;
    isEnabled: boolean;
    config: VideoCropConfig;
    stats: any;
    enableCropping: boolean;
    cropRegion: CropRegion | null;
    subscriberCount: number;
    frameCount: number;
    hasOriginalTrack: boolean;
    hasProcessedTrack: boolean;
  } {
    return {
      ...this.getMetadata(),
      enableCropping: this.config.enableCropping || false,
      cropRegion: this.config.cropRegion || this.config.defaultCropRegion || null,
      subscriberCount: this.subscribers.size,
      frameCount: this.frameCount,
      hasOriginalTrack: false, // These would need to be tracked in base class
      hasProcessedTrack: false,
    };
  }

  /**
   * Override cleanup to handle video-specific resources
   */
  override async cleanup(): Promise<void> {
    this.log('Cleaning up video interceptor...');

    // Clear subscribers
    this.subscribers.clear();

    // Call parent cleanup
    await super.cleanup();

    this.log('Video interceptor cleanup complete');
  }

  /**
   * Utility function to ensure even numbers for YUV 4:2:0 alignment
   */
  private makeEven(value: number): number {
    return Math.floor(value / 2) * 2;
  }
}

// Export for use in other modules
export default VideoFrameInterceptor;

// Make available globally for injection scripts
if (typeof window !== 'undefined') {
  (window as any).VideoFrameInterceptor = VideoFrameInterceptor;
}
