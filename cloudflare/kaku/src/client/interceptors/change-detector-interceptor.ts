/**
 * Change Detector Interceptor
 *
 * A video frame interceptor that detects click events and
 * pauses the video stream when screen changes are detected, resuming only
 * after the screen has stabilized.
 *
 * Features:
 * - Click event detection
 * - Screen stability detection using pixelmatch
 * - Stream pause/resume functionality
 * - Configurable thresholds and stability duration
 * - Integration with established interceptor architecture
 */

import BaseInterceptor from './base-interceptor.js';
import type { ChangeDetectorConfig } from '../types';

interface ControlTabManager {
  sendMessage(message: any): void;
}

class ChangeDetectorInterceptor extends BaseInterceptor {
  // Canvas for frame processing and comparison
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;

  // Change detector state
  private isStreamPaused: boolean = false;
  private isMonitoring: boolean = false;
  private lastFrameData: Uint8ClampedArray | null = null;
  private consecutiveStableCount: number = 0;
  private maxWaitTimeout: NodeJS.Timeout | null = null;

  // Frame tracking
  private pendingFrames: VideoFrame[] = [];
  private lastStableFrame: VideoFrame | null = null;

  // WebSocket messaging
  private controlTabManager: ControlTabManager | null = null;
  private triggeringWebClientId: string | null = null;
  private pauseStartTime: number | null = null;

  declare config: ChangeDetectorConfig & {
    comparisonInterval: number;
  };

  constructor(name: string = 'change-detector', options: ChangeDetectorConfig = {}) {
    // Default configuration
    const defaultConfig = {
      debug: false,
      enabled: true,
      // Change detector settings
      changeThreshold: 5, // Percentage change to trigger pause
      stabilityThreshold: 1, // Percentage change to consider stable
      consecutiveStableFrames: 3, // Number of consecutive stable frames required
      maxWaitDuration: 5000, // Maximum time to wait for stability
      // Performance settings
      pixelSampling: 2, // Pixel sampling rate for performance
      comparisonInterval: 100, // Interval for frame comparison
      ...options,
    };

    super(name, defaultConfig);

    this.log('ChangeDetectorInterceptor initialized', this.config);
  }

  /**
   * Set the control tab manager reference for WebSocket messaging
   */
  setControlTabManager(controlTabManager: ControlTabManager): void {
    this.controlTabManager = controlTabManager;
    this.log('Control tab manager reference set for WebSocket messaging');
  }

  /**
   * Set the web client ID that triggered the change detection
   */
  setTriggeringWebClient(webClientId: string): void {
    this.triggeringWebClientId = webClientId;
    this.log(`Triggering web client set: ${webClientId}`);
  }

  /**
   * Send WebSocket notification to the triggering web client
   */
  private sendWebSocketNotification(type: string, data: Record<string, any> = {}): void {
    if (!this.controlTabManager || !this.triggeringWebClientId) {
      this.log('Cannot send WebSocket notification - missing control tab manager or web client ID');
      return;
    }

    const message = {
      type: type,
      webClientId: this.triggeringWebClientId,
      timestamp: Date.now(),
      ...data,
    };

    this.log(`Sending WebSocket notification: ${type} to client ${this.triggeringWebClientId}`);
    this.controlTabManager.sendMessage(message);
  }

  /**
   * Process a video frame - implements BaseInterceptor interface
   */
  async processVideoFrame(frame: VideoFrame): Promise<VideoFrame> {
    try {
      // If not monitoring, pass through
      if (!this.isMonitoring) {
        return frame;
      }

      // Initialize canvas if needed
      if (
        !this.canvas ||
        this.canvas.width !== frame.codedWidth ||
        this.canvas.height !== frame.codedHeight
      ) {
        this.initializeCanvas(frame.codedWidth, frame.codedHeight);
      }

      if (!this.ctx) {
        throw new Error('Canvas context not available');
      }

      // Draw frame to canvas and get image data
      this.ctx.drawImage(frame, 0, 0);
      const imageData = this.ctx.getImageData(0, 0, this.canvas!.width, this.canvas!.height);
      const currentFrameData = imageData.data;

      // Compare with previous frame if available
      if (this.lastFrameData) {
        const changePercentage = this.compareFrames(currentFrameData, this.lastFrameData);

        if (this.config.debug) {
          this.log(`Frame comparison: ${changePercentage.toFixed(2)}% change`);
        }

        // Check if significant change detected - immediately pause and return held frame
        if (changePercentage > (this.config.changeThreshold || 5) && !this.isStreamPaused) {
          this.log(
            `📊 Significant change detected: ${changePercentage.toFixed(
              2,
            )}% > ${this.config.changeThreshold}% - immediately pausing`,
          );
          this.pauseStream();

          // Return the last stable frame instead of the changed frame
          if (this.lastStableFrame) {
            frame.close(); // Close the changed frame
            return this.lastStableFrame;
          }
        }
        // Check if screen has stabilized
        else if (this.isStreamPaused && changePercentage <= (this.config.stabilityThreshold || 1)) {
          this.handleStableFrame();
        }
        // Reset stability if change detected while monitoring stability
        else if (this.isStreamPaused && changePercentage > (this.config.stabilityThreshold || 1)) {
          this.resetStability();
        }
      }

      // Store current frame data for next comparison
      this.lastFrameData = new Uint8ClampedArray(currentFrameData);

      // If not paused, store this as the last stable frame
      if (!this.isStreamPaused) {
        if (this.lastStableFrame) {
          this.lastStableFrame.close();
        }
        this.lastStableFrame = new VideoFrame(frame, {
          timestamp: frame.timestamp,
          duration: frame.duration ?? undefined,
        });
      }

      // Return the frame (or held frame if paused)
      return this.isStreamPaused && this.lastStableFrame ? this.lastStableFrame : frame;
    } catch (error) {
      this.log('Error in change detector:', error);
      return frame;
    }
  }

  /**
   * Initialize canvas for frame processing
   */
  private initializeCanvas(width: number, height: number): void {
    this.canvas = document.createElement('canvas');
    this.canvas.width = width;
    this.canvas.height = height;
    this.ctx = this.canvas.getContext('2d');

    if (!this.ctx) {
      throw new Error('Failed to get 2D canvas context');
    }

    this.log(`Canvas initialized: ${width}x${height}`);
  }

  /**
   * Compare two frames and return percentage difference
   */
  private compareFrames(currentData: Uint8ClampedArray, previousData: Uint8ClampedArray): number {
    let diffPixels = 0;
    const totalPixels = currentData.length / 4; // RGBA = 4 bytes per pixel
    const sampling = this.config.pixelSampling || 2;

    for (let i = 0; i < currentData.length; i += 4 * sampling) {
      const rDiff = Math.abs(currentData[i] - previousData[i]);
      const gDiff = Math.abs(currentData[i + 1] - previousData[i + 1]);
      const bDiff = Math.abs(currentData[i + 2] - previousData[i + 2]);

      // Consider pixel changed if any channel differs by more than threshold
      if (rDiff > 30 || gDiff > 30 || bDiff > 30) {
        diffPixels++;
      }
    }

    return (diffPixels / (totalPixels / sampling)) * 100;
  }

  /**
   * Pause the stream
   */
  private pauseStream(): void {
    this.isStreamPaused = true;
    this.pauseStartTime = Date.now();
    this.consecutiveStableCount = 0;

    // Set maximum wait timeout
    if (this.maxWaitTimeout) {
      clearTimeout(this.maxWaitTimeout);
    }

    this.maxWaitTimeout = setTimeout(() => {
      this.log('Maximum wait duration reached, resuming stream');
      this.resumeStream();
    }, this.config.maxWaitDuration || 5000);

    this.sendWebSocketNotification('stream-paused', {
      reason: 'change-detected',
      pauseStartTime: this.pauseStartTime,
    });
  }

  /**
   * Handle stable frame detection
   */
  private handleStableFrame(): void {
    this.consecutiveStableCount++;
    this.log(`Stable frame ${this.consecutiveStableCount}/${this.config.consecutiveStableFrames}`);

    if (this.consecutiveStableCount >= (this.config.consecutiveStableFrames || 3)) {
      this.resumeStream();
    }
  }

  /**
   * Reset stability counter
   */
  private resetStability(): void {
    this.consecutiveStableCount = 0;
  }

  /**
   * Resume the stream
   */
  private resumeStream(): void {
    const pauseDuration = this.pauseStartTime ? Date.now() - this.pauseStartTime : 0;

    this.isStreamPaused = false;
    this.consecutiveStableCount = 0;
    this.pauseStartTime = null;

    if (this.maxWaitTimeout) {
      clearTimeout(this.maxWaitTimeout);
      this.maxWaitTimeout = null;
    }

    this.log(`Stream resumed after ${pauseDuration}ms`);
    this.sendWebSocketNotification('stream-resumed', {
      pauseDuration,
      resumeTime: Date.now(),
    });
  }

  /**
   * Start monitoring for changes
   */
  startMonitoring(): void {
    this.isMonitoring = true;
    this.log('Change detection monitoring started');
  }

  /**
   * Stop monitoring for changes
   */
  stopMonitoring(): void {
    this.isMonitoring = false;
    this.isStreamPaused = false;
    this.consecutiveStableCount = 0;

    if (this.maxWaitTimeout) {
      clearTimeout(this.maxWaitTimeout);
      this.maxWaitTimeout = null;
    }

    this.log('Change detection monitoring stopped');
  }

  /**
   * Override cleanup to handle change detector resources
   */
  override async cleanup(): Promise<void> {
    this.log('Cleaning up change detector interceptor...');

    this.stopMonitoring();

    // Clean up canvas resources
    if (this.canvas) {
      this.canvas.width = 0;
      this.canvas.height = 0;
      this.canvas = null;
    }

    this.ctx = null;
    this.lastFrameData = null;

    // Clean up frame references
    if (this.lastStableFrame) {
      this.lastStableFrame.close();
      this.lastStableFrame = null;
    }

    // Clean up pending frames
    for (const frame of this.pendingFrames) {
      frame.close();
    }
    this.pendingFrames = [];

    // Call parent cleanup
    await super.cleanup();

    this.log('Change detector interceptor cleanup complete');
  }
}

// Export for use in other modules
export default ChangeDetectorInterceptor;

// Make available globally for injection scripts
if (typeof window !== 'undefined') {
  (window as any).ChangeDetectorInterceptor = ChangeDetectorInterceptor;
}
