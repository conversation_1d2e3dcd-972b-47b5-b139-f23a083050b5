import { Connection } from 'agents';
import { ConnectionState } from './connection-agent';
import {
  WebClientReadyEvent,
  WebClientAnswerEvent,
  WebClientCandidateEvent,
  TabStreamerOfferEvent,
  TabStreamerAnswerEvent,
  TabStreamerCandidateEvent,
  ForwardedWebClientReadyEvent,
  ForwardedWebClientAnswerEvent,
  ForwardedWebClientCandidateEvent,
  BaseWebSocketEvent,
  WebRTCEvent,
} from './types/websocket-events';

export const getWsConnectionsOfType = (
  connections: Connection<ConnectionState>[],
  type: ConnectionState['clientType'],
): Connection<ConnectionState>[] => {
  return connections.filter((c) => c.state?.clientType === type);
};

/**
 * WebRTC Message Router - Handles client-specific routing for WebRTC signaling messages
 *
 * This utility class ensures that WebRTC messages are routed to the correct specific clients
 * rather than being broadcast to all clients of a type. It supports multiple concurrent
 * WebRTC connections without collision by using webClientId for targeted routing.
 */
export class WebRTCMessageRouter {
  private connections: Connection<ConnectionState>[];
  private broadcast: (message: string, exclude?: string[]) => void;
  private logger: (message: string, ...args: any[]) => void;
  private debug = true;
  constructor(
    connections: Connection<ConnectionState>[],
    broadcast: (message: string, exclude?: string[]) => void,
    logger: (message: string, ...args: any[]) => void = (message, ...args) => {
      if (this.debug) {
        console.log(message, ...args);
      }
    },
  ) {
    this.connections = connections;
    this.broadcast = broadcast;
    this.logger = logger;
  }

  /**
   * Routes a WebRTC message based on its type and content
   * @param event - The WebRTC event to route
   * @param senderConnection - The connection that sent the message
   */
  routeWebRTCMessage(event: WebRTCEvent, senderConnection: Connection<ConnectionState>): void {
    switch (event.type) {
      case 'web-client-ready':
        this.handleWebClientReadyMessage(event as WebClientReadyEvent, senderConnection);
        break;
      case 'web-client-answer':
        this.handleWebClientAnswerMessage(event as WebClientAnswerEvent, senderConnection);
        break;
      case 'web-client-candidate':
        this.handleWebClientCandidateMessage(event as WebClientCandidateEvent, senderConnection);
        break;
      case 'tab-streamer-offer':
        this.handleTabStreamerOfferMessage(event as TabStreamerOfferEvent, senderConnection);
        break;
      case 'tab-streamer-answer':
        this.handleTabStreamerAnswerMessage(event as TabStreamerAnswerEvent, senderConnection);
        break;
      case 'tab-streamer-candidate':
        this.handleTabStreamerCandidateMessage(
          event as TabStreamerCandidateEvent,
          senderConnection,
        );
        break;
      default:
        this.logger('❌ [WebRTC Router] Unknown WebRTC message type:', event);
    }
  }

  /**
   * Handles 'web-client-ready' messages from web clients
   * Adds webClientId and forwards to all tab-streamers
   */
  private handleWebClientReadyMessage(
    event: WebClientReadyEvent,
    senderConnection: Connection<ConnectionState>,
  ): void {
    const messageWithClientId: ForwardedWebClientReadyEvent = {
      ...event,
      webClientId: senderConnection.id,
    };

    const tabStreamers = getWsConnectionsOfType(this.connections, 'tab-streamer');
    tabStreamers.forEach((connection) => {
      this.sendMessage(connection, messageWithClientId);
    });
  }

  /**
   * Handles 'tab-streamer-offer' messages from tab-streamers
   * Routes to specific web client using webClientId
   */
  private handleTabStreamerOfferMessage(
    event: TabStreamerOfferEvent,
    _: Connection<ConnectionState>,
  ): void {
    const targetWebClientId = event.webClientId;

    const targetConnection = this.findConnectionById(targetWebClientId);
    if (!targetConnection) {
      this.logger('❌ [WebRTC Router] Target web client not found:', targetWebClientId);
      return;
    }
    this.sendMessage(targetConnection, event);
  }

  /**
   * Handles 'web-client-answer' messages from web clients
   * Adds webClientId and forwards to all tab-streamers
   */
  private handleWebClientAnswerMessage(
    event: WebClientAnswerEvent,
    senderConnection: Connection<ConnectionState>,
  ): void {
    // Add webClientId to the message
    const messageWithClientId: ForwardedWebClientAnswerEvent = {
      ...event,
      webClientId: senderConnection.id,
    };

    // Send to all tab-streamers
    const tabStreamers = getWsConnectionsOfType(this.connections, 'tab-streamer');
    tabStreamers.forEach((connection) => {
      this.sendMessage(connection, messageWithClientId);
    });
  }

  /**
   * Handles 'tab-streamer-answer' messages from tab-streamers
   * Routes to specific web client using webClientId
   */
  private handleTabStreamerAnswerMessage(
    event: TabStreamerAnswerEvent,
    _: Connection<ConnectionState>,
  ): void {
    const targetWebClientId = event.webClientId;

    const targetConnection = this.findConnectionById(targetWebClientId);
    if (!targetConnection) {
      this.logger('❌ [WebRTC Router] Target web client not found:', targetWebClientId);
      return;
    }
    this.sendMessage(targetConnection, event);
  }

  /**
   * Handles 'web-client-candidate' messages from web clients
   * Adds webClientId and forwards to all tab-streamers
   */
  private handleWebClientCandidateMessage(
    event: WebClientCandidateEvent,
    senderConnection: Connection<ConnectionState>,
  ): void {
    // Add webClientId to the message
    const messageWithClientId: ForwardedWebClientCandidateEvent = {
      ...event,
      webClientId: senderConnection.id,
    };

    // Send to all tab-streamers
    const tabStreamers = getWsConnectionsOfType(this.connections, 'tab-streamer');
    tabStreamers.forEach((connection) => {
      this.sendMessage(connection, messageWithClientId);
    });
  }

  /**
   * Handles 'tab-streamer-candidate' messages from tab-streamers
   * Routes to specific web client using webClientId
   */
  private handleTabStreamerCandidateMessage(
    event: TabStreamerCandidateEvent,
    connection: Connection<ConnectionState>,
  ): void {
    this.broadcast(JSON.stringify(event), [connection.id]);
  }

  /**
   * Finds a connection by its ID
   * @param connectionId - The ID of the connection to find
   * @returns The connection if found, undefined otherwise
   */
  private findConnectionById(connectionId: string): Connection<ConnectionState> | undefined {
    return this.connections.find((connection) => connection.id === connectionId);
  }

  /**
   * Sends a message to a specific connection
   * @param connection - The connection to send the message to
   * @param message - The message to send
   */
  private sendMessage(connection: Connection<ConnectionState>, message: BaseWebSocketEvent): void {
    try {
      connection.send(JSON.stringify(message));
      this.logger(
        '✅ [WebRTC Router] Message sent to',
        connection.state?.clientType,
        connection.id,
      );
    } catch (error) {
      this.logger('❌ [WebRTC Router] Failed to send message to', connection.id, ':', error);
    }
  }

  /**
   * Updates the connections array (call this when connections change)
   * @param connections - Updated array of connections
   */
  updateConnections(connections: Connection<ConnectionState>[]): void {
    this.connections = connections;
  }
}
