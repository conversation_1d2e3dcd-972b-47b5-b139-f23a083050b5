/**
 * WebSocket event types for consistent event handling
 */

/**
 * Base interface for all WebSocket events
 */
export interface BaseWebSocketEvent {
  type: string;
  [key: string]: any;
}

/**
 * Terms and conditions events
 */
export interface AgreeAndContinueEvent extends BaseWebSocketEvent {
  type: 'agree-and-continue';
}

export interface DeclineTermsEvent extends BaseWebSocketEvent {
  type: 'decline-terms';
}

/**
 * Form submission events
 */
export interface FormSubmissionEvent extends BaseWebSocketEvent {
  type: 'form-submission';
  clickId: string;
  interaction: 'submit' | 'click';
  [fieldName: string]: string | any; // Form field values
}

/**
 * UI interaction events
 */
export interface CropBoxUpdateEvent extends BaseWebSocketEvent {
  type: 'cropbox-update';
  // Add specific cropbox update properties here
}

export interface RetryEvent extends BaseWebSocketEvent {
  type: 'retry';
}

export interface CaptchaVerificationEvent extends BaseWebSocketEvent {
  type: 'captcha-verification';
  status: 'completed';
  timestamp: number;
}

export interface SocialLoginReconnectedEvent extends BaseWebSocketEvent {
  type: 'social-login-reconnected';
  timestamp: number;
  viewport: { width: number; height: number };
  wsEndpoint: string;
}

// Events sent FROM web-client TO tab-streamer
export interface WebClientReadyEvent extends BaseWebSocketEvent {
  type: 'web-client-ready';
}

export interface WebClientAnswerEvent extends BaseWebSocketEvent {
  type: 'web-client-answer';
  answer: RTCSessionDescriptionInit;
}

export interface WebClientCandidateEvent extends BaseWebSocketEvent {
  type: 'web-client-candidate';
  candidate: RTCIceCandidateInit;
}

// Events sent FROM tab-streamer TO web-client
export interface TabStreamerOfferEvent extends BaseWebSocketEvent {
  type: 'tab-streamer-offer';
  offer: RTCSessionDescriptionInit;
  webClientId: string;
}

export interface TabStreamerAnswerEvent extends BaseWebSocketEvent {
  type: 'tab-streamer-answer';
  answer: RTCSessionDescriptionInit;
  webClientId: string;
}

export interface TabStreamerCandidateEvent extends BaseWebSocketEvent {
  type: 'tab-streamer-candidate';
  candidate: RTCIceCandidateInit;
}

export interface ForwardedWebClientReadyEvent extends BaseWebSocketEvent {
  type: 'web-client-ready';
  webClientId: string;
}

export interface ForwardedWebClientAnswerEvent extends BaseWebSocketEvent {
  type: 'web-client-answer';
  answer: RTCSessionDescriptionInit;
  webClientId: string;
}

export interface ForwardedWebClientCandidateEvent extends BaseWebSocketEvent {
  type: 'web-client-candidate';
  candidate: RTCIceCandidateInit;
  webClientId: string;
}

export type WebClientWebRTCEvent =
  | WebClientReadyEvent
  | WebClientAnswerEvent
  | WebClientCandidateEvent;

export type TabStreamerWebRTCEvent =
  | TabStreamerOfferEvent
  | TabStreamerAnswerEvent
  | TabStreamerCandidateEvent;

export type ForwardedWebRTCEvent =
  | ForwardedWebClientReadyEvent
  | ForwardedWebClientAnswerEvent
  | ForwardedWebClientCandidateEvent;

export type WebRTCEvent = WebClientWebRTCEvent | TabStreamerWebRTCEvent;

/**
 * Union type of all possible WebSocket events
 */
export type WebSocketEvent =
  | AgreeAndContinueEvent
  | DeclineTermsEvent
  | FormSubmissionEvent
  | CropBoxUpdateEvent
  | RetryEvent
  | CaptchaVerificationEvent
  | SocialLoginReconnectedEvent
  | WebRTCEvent;

/**
 * Event handler type definitions
 */
export type EventHandler<T extends BaseWebSocketEvent> = (
  event: T,
  connection?: any,
) => Promise<void> | void;

export interface EventHandlers {
  'agree-and-continue': EventHandler<AgreeAndContinueEvent>;
  'decline-terms': EventHandler<DeclineTermsEvent>;
  'form-submission': EventHandler<FormSubmissionEvent>;
  'cropbox-update': EventHandler<CropBoxUpdateEvent>;
  retry: EventHandler<RetryEvent>;
  'captcha-verification': EventHandler<CaptchaVerificationEvent>;
  'web-client-ready': EventHandler<WebClientReadyEvent>;
  'web-client-answer': EventHandler<WebClientAnswerEvent>;
  'web-client-candidate': EventHandler<WebClientCandidateEvent>;
  'tab-streamer-offer': EventHandler<TabStreamerOfferEvent>;
  'tab-streamer-answer': EventHandler<TabStreamerAnswerEvent>;
  'tab-streamer-candidate': EventHandler<TabStreamerCandidateEvent>;
}
