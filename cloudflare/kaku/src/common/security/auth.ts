import { env } from 'cloudflare:workers';
import { basicAuth } from 'hono/basic-auth';
import { createErrorDetail, formatErrorResponse } from '../web/httpHelpers';
import { SupportedLocales } from '../web/localizationMessages';
import { MiddlewareHandler } from 'hono';
import { KakuApp, TokenInfo, TokenInfoSchema } from '../types';
import { User, UserSchema } from '../../user/User';

export const validateAuth = async (request: Request): Promise<Response | User> => {
  const url = new URL(request.url);
  const headers = new Headers(request.headers);
  const isAuthHeaderPresent = headers.has('Authorization') || headers.has('X-Auth-Token');

  if (!isAuthHeaderPresent) {
    const tokenInfoOrResponse = await login(request, url);

    if (tokenInfoOrResponse instanceof Response) {
      return tokenInfoOrResponse;
    }

    headers.set('X-Auth-Token', tokenInfoOrResponse.xAuthToken);
  }

  const userResponse = await fetch(`${env.SUNNY_API_ENDPOINT}/v1/users`, {
    method: 'GET',
    headers: headers,
  });
  switch (userResponse.status) {
    case 200:
      return UserSchema.parse(await userResponse.json());
    case 401:
      return unauthenticatedResponse(request);
    default:
      return userResponse;
  }
};

const login = async (request: Request, url: URL): Promise<TokenInfo | Response> => {
  const magicLinkToken = url.searchParams.get('token');
  if (!magicLinkToken) {
    return unauthenticatedResponse(request);
  }

  const newHeaders = new Headers(request.headers);
  newHeaders.set('Authorization', `Magic ${magicLinkToken}`);

  const response = await fetch(`${env.SUNNY_API_ENDPOINT}/v1/login`, {
    method: 'POST',
    headers: newHeaders,
  });

  if (!response.ok) {
    return response;
  }

  return TokenInfoSchema.parse(await response.json());
};

const unauthenticatedResponse = (request: Request) => {
  const url = new URL(request.url);
  const locale = (request.headers.get('Accept-Language') ?? 'en') as SupportedLocales;
  const path = url.pathname;

  return new Response(
    JSON.stringify(
      formatErrorResponse({
        path,
        method: request.method,
        statusCode: 401,
        detail: [createErrorDetail('UNAUTHENTICATED', locale)],
      }),
    ),
    {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    },
  );
};

export const authMiddleware: MiddlewareHandler<KakuApp> = async (c, next) => {
  const userOrResponse = await validateAuth(c.req.raw);

  if (userOrResponse instanceof Response) {
    return userOrResponse;
  }

  // Add user to context
  c.set('user', userOrResponse);

  await next();
};

export const basicAuthMiddleware: MiddlewareHandler<{ Bindings: Env }> = async (c, next) => {
  const username = c.env.CLOUDFLARE_INTEGRATION_USER_USERNAME;
  const password = c.env.CLOUDFLARE_INTEGRATION_USER_PASSWORD;
  try {
    await basicAuth({ username, password })(c, next);
  } catch (e) {
    return c.json({ status: 'error', message: 'Authentication failed' }, 401);
  }
};
