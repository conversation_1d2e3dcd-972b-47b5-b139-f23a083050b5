/**
 * TypeScript interfaces for client-side browser scripts
 *
 * This module provides comprehensive type definitions for all client-side browser scripts
 * that are injected into browser contexts. These interfaces mirror the methods exposed
 * on window objects by injected client scripts, enabling type-safe interaction through
 * the CDP abstraction layer.
 *
 * @example
 * ```typescript
 * import { withCdp } from '../client-script-abstraction';
 * import type { <PERSON><PERSON>, BrowserController } from './client-scripts';
 *
 * const clientScripts = withCdp(cdp, executionContextId, sessionId);
 * await clientScripts.BrowserController.init();
 * ```
 */

/**
 * Viewport dimensions for browser operations
 */
export interface Viewport {
  /** Width in pixels */
  width: number;
  /** Height in pixels */
  height: number;
}

/**
 * Bounding box coordinates for UI elements or screen regions
 */
export interface BoundingBox {
  /** X coordinate (left edge) */
  x: number;
  /** Y coordinate (top edge) */
  y: number;
  /** Width in pixels */
  width: number;
  /** Height in pixels */
  height: number;
}

/**
 * Result from screenshot capture operations
 */
export interface ScreenshotResult {
  /** Base64-encoded screenshot data */
  data?: string;
  /** Whether the operation was successful */
  success?: boolean;
  /** Error message if operation failed */
  error?: string;
  /** Whether this is a fallback result */
  fallback?: boolean;
  /** Time taken to process the screenshot in milliseconds */
  processingTime?: number;
  /** Whether PII redaction was applied */
  redactionApplied?: boolean;
  /** Detailed statistics about PII redaction */
  redactionStats?: {
    /** Number of items that were redacted */
    redactedCount?: number;
    /** Time taken for redaction processing in milliseconds */
    processingTime?: number;
    /** Mappings of redacted elements */
    mappings?: Array<{
      /** Type of HTML element that was redacted */
      elementType: string;
      [key: string]: unknown;
    }>;
    /** Statistics about restoration operations */
    restorationStats?: {
      /** Number of items successfully restored */
      restoredCount?: number;
      /** Number of items that failed to restore */
      failedCount?: number;
      /** Array of error messages from restoration failures */
      errors?: string[];
    };
  };
}

/**
 * Page metadata and information
 */
export interface PageInfo {
  /** Current page URL */
  url: string;
  /** Page title */
  title: string;
  /** Additional page properties */
  [key: string]: unknown;
}

/**
 * Input event data for user interactions
 */
export interface InputEventData {
  /** Type of input event (e.g., 'click', 'keydown', 'text-insert') */
  type: string;
  /** X coordinate for mouse events */
  x?: number;
  /** Y coordinate for mouse events */
  y?: number;
  /** Key code or key name for keyboard events */
  key?: string;
  /** Text content for text insertion events */
  text?: string;
  /** Additional event properties */
  [key: string]: unknown;
}

export interface CaptchaDetectorConfig {
  diffThreshold?: number;
  screenshotQuality?: number;
  debug?: boolean;
  comparisonIntervalMs?: number;
  sampling?: number;
  settleThreshold?: number;
  consecutiveSettledFrames?: number;
  maxWaitDuration?: number;
}

export interface ComparisonResult {
  percentageDiff: number;
  numDiffPixels: number;
  comparisonTime: number;
}

export interface TensorFlowDetectorConfig {
  scoreThreshold?: number;
  debug?: boolean;
  inputSize?: number;
  maxDetections?: number;
}

export interface DetectionResult {
  class: string;
  score: number;
  bbox: BoundingBox;
}

export interface ScreenshotComparisonOptions {
  threshold?: number;
  includeAA?: boolean;
  diffMask?: boolean;
  sampling?: number;
}

export interface ImageData {
  data: Uint8ClampedArray;
  width: number;
  height: number;
}

export interface Config {
  scoreThreshold: number;
  debug: boolean;
  inputSize: number;
  detectUnresolved?: boolean;
  detectResolved?: boolean;
  preferredClass?: 'unresolved' | 'resolved' | 'both';
}

interface BBox {
  ymin: number;
  xmin: number;
  ymax: number;
  xmax: number;
}

export interface Detection {
  class?: number;
  className: string;
  score: number;
  bbox: BBox;
  boundingBox?: BoundingBox;
}

interface ClassStat {
  count: number;
  maxScore: number;
  avgScore: number;
}

export interface DetectionStats {
  captcha_unresolved: ClassStat;
  captcha_resolved: ClassStat;
  totalBoxes: number;
  totalDetections: number;
}

export interface DetectionResults {
  detections: Detection[];
  highestScoringDetection: Detection | null;
  stats?: DetectionStats;
  latency?: number;
}