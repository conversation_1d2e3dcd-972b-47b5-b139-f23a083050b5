import { Hono } from 'hono';
import { z } from 'zod';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../common/types';
import { HyperbrowserService } from '../../workflow/services/HyperbrowserService';
import { basicAuthMiddleware } from '../../common/security/auth';
import { CDP } from '../../browser/simple-cdp';

const CreateSessionRequestBodySchema = z.object({
  proxyIp: z.string(),
  proxyUsername: z.string().optional(),
  proxyPassword: z.string().optional(),
  timezone: z.string().optional(),
  sessionTimeoutMinutes: z.number().optional(),
});

const app = new Hono<KakuApp>();

app.post('/session', basicAuthMiddleware, async (c) => {
  try {
    const apiKey = c.env.HYPERBROWSER_API_KEY;

    const hyperbrowserService = new HyperbrowserService(apiKey);

    const parsedBody = CreateSessionRequestBodySchema.safeParse(await c.req.json());

    if (!parsedBody.success) {
      return c.json(
        { status: 'error', message: 'Invalid request body', errors: parsedBody.error.errors },
        400,
      );
    }

    const body = parsedBody.data;

    const session = await hyperbrowserService.createSession({
      timeoutMinutes: body.sessionTimeoutMinutes ? body.sessionTimeoutMinutes : 120,
      proxyConfig: {
        proxyServer: body.proxyIp ? body.proxyIp : 'http://us.proxy.geonode.io:10000',
        password: body.proxyPassword ? body.proxyPassword : c.env.GEONODE_PASSWORD,
        username: body.proxyUsername ? body.proxyUsername : c.env.GEONODE_USERNAME,
      },
      useProxy: true,
    });

    let errorMessage: string | null = null;
    try {
      const cdpClient = new CDP({ webSocketDebuggerUrl: session.wsEndpoint });

      // Create a control tab and set up the session
      const controlTabTarget = await cdpClient.Target.createTarget({ url: 'about:blank' });
      const targetResponse = await cdpClient.Target.attachToTarget({
        targetId: controlTabTarget.targetId,
        flatten: true,
      });
      const sessionId = targetResponse.sessionId;

      // Enable the necessary domains
      await cdpClient.Page.enable(undefined, sessionId);
      await cdpClient.Runtime.enable(undefined, sessionId);

      // Set the timezone override
      await cdpClient.Emulation.setTimezoneOverride(
        {
          timezoneId: body.timezone ? body.timezone : TIMEZONE,
        },
        sessionId,
      );

      // Navigate to a URL
      await cdpClient.Page.navigate({ url: 'https://www.ip-score.com' }, sessionId);

      // Verify the timezone setting
      const res = await cdpClient.Runtime.evaluate(
        { expression: 'Intl.DateTimeFormat().resolvedOptions().timeZone', returnByValue: true },
        sessionId,
      );

      console.log('reported tz:', res.result.value);

      // Inject a custom script to listen for attachedToTarget events in the remote browser
      const injectListenerScript = INJECTED_SCRIPT({
        wsEndpoint: session.wsEndpoint,
        timezone: body.timezone ? body.timezone : TIMEZONE,
      });

      // Execute the script in the control tab
      await cdpClient.Runtime.evaluate({ expression: injectListenerScript }, sessionId);
    } catch (error) {
      console.error('Failed to set timezone override:', error);
      errorMessage = error instanceof Error ? error.message : 'Unknown error';
    }

    return c.json({
      status: 'success',
      message: errorMessage
        ? `Failed to set timezone override: ${errorMessage}`
        : 'Hyperbrowser session created',
      sessionId: session.sessionId,
      sessionLink: session.wsEndpoint,
      liveUrl: session.liveUrl,
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Failed to create Hyperbrowser session:', errorMessage);
    return c.json(
      { status: 'error', message: `Failed to create Hyperbrowser session: ${errorMessage}` },
      500,
    );
  }
});

export { app as HyperbrowserHandler };

const TIMEZONE = 'America/Denver';

const INJECTED_SCRIPT = ({ wsEndpoint, timezone }: { wsEndpoint: string; timezone: string }) => `
          (() => {
            console.log('Connecting to CDP WebSocket at ${wsEndpoint}');
            const cdConnection = new WebSocket('${wsEndpoint}');
            
            cdConnection.onopen = () => {
              console.log('WebSocket connection opened');
              cdConnection.send(JSON.stringify({
                id: 1,
                method: 'Target.setAutoAttach',
                params: {
                  autoAttach: true,
                  flatten: true,
                  waitForDebuggerOnStart: false
                }
              }));
              console.log('Sent Target.setAutoAttach');
            };
            
            cdConnection.onmessage = (event) => {
              const message = JSON.parse(event.data);
              console.log('Received message:', message);
              
              if (message.method === 'Target.attachedToTarget') {
                const newSessionId = message.params.sessionId;
                console.log('Attached to new target, setting timezone:', newSessionId);
                cdConnection.send(JSON.stringify({
                  id: 2,
                  sessionId: newSessionId,
                  method: 'Emulation.setTimezoneOverride',
                  params: { timezoneId: '${timezone}' }
                }));
                console.log('Set timezone for new session:', newSessionId);
              }
            };
            
            cdConnection.onerror = (error) => {
              console.error('WebSocket error:', error);
            };
            
            cdConnection.onclose = () => {
              console.log('WebSocket connection closed');
            };
          })();
        `;
