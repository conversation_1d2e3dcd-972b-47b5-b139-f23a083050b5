import { SunnyClient, ConnectionEvent } from './SunnyClient';

export class ProdSunnyClient implements SunnyClient {
  private readonly sunnyApiEndpoint: string;
  private readonly authHeader: string;

  constructor(env: Env) {
    this.sunnyApiEndpoint = env.SUNNY_API_ENDPOINT;
    this.authHeader = `Basic ${btoa(`${env.CLOUDFLARE_INTEGRATION_USER_USERNAME}:${env.CLOUDFLARE_INTEGRATION_USER_PASSWORD}`)}`;
  }

  async sendConnectionEvent(connectionEvent: ConnectionEvent): Promise<void> {
    const response = await fetch(`${this.sunnyApiEndpoint}/v1/internal/connections/event`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Impersonated-UserId': connectionEvent.userId,
        'Authorization': this.authHeader,
        'Idempotency-Key': crypto.randomUUID(),
      },
      body: JSON.stringify(connectionEvent),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(
        `Failed to send connection event to Sunny API: ${response.status} ${response.statusText} - ${errorData}`,
      );
    }
  }
}
