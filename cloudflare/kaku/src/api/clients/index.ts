import { SunnyClient } from './SunnyClient';
import { LocalSunnyClient } from './LocalSunnyClient';
import { ProdSunnyClient } from './ProdSunnyClient';

export { SunnyClient } from './SunnyClient';

export function createSunnyClient(env: Env): SunnyClient {
  if (env.ENVIRONMENT === 'prod' || env.ENVIRONMENT === 'dev') { // TODO: remove this dev environment support after testing integration with <PERSON>
    return new ProdSunnyClient(env);
  } else {
    return new LocalSunnyClient();
  }
}
