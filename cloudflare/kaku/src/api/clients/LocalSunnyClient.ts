import { Sunny<PERSON>lient, ConnectionEvent } from './SunnyClient';

export class LocalSunnyClient implements SunnyClient {
  async sendConnectionEvent(connectionEvent: ConnectionEvent): Promise<void> {
    console.log('[LocalSunnyClient] Simulating connection event:', {
      userId: connectionEvent.userId,
      connectionStatus: connectionEvent.connectionStatus,
      connectionId: connectionEvent.connectionId,
      platform: connectionEvent.platform,
    });
    return Promise.resolve();
  }
}
