import { Hono } from 'hono';
import { agentsMiddleware } from 'hono-agents';
import { <PERSON>kuApp } from '../common/types';
import { ConnectionsHandler, InternalHandler, App<PERSON>andler, HyperbrowserHandler } from './handlers';

const app = new Hono<KakuApp>();

// Note: Agent class names are transformed to kebab-case in URLs
// Example: ConnectionAgent → /agents/connection-agent/[platformId]
// In our case, it's /agents/connections/[platformId]
app
  .use(
    '*',
    agentsMiddleware({
      options: {
        cors: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
        prefix: 'v1/agents',
      },
      onError: (error) => {
        console.error('Agent middleware error:', error);
      },
    }),
  )
  .route('/cf/v1/connections', ConnectionsHandler)
  .route('/cf/web/v1/connections', ConnectionsHandler)
  .route('/v1/internal', InternalHandler)
  .route('/v1/internal/hyperbrowser', HyperbrowserHandler)
  .route('/', AppHandler);

export default app;

export { Connections } from '../agent/connection-agent';
export { CoordinatorDO } from '../coordinator/coordinator-do';
export { ConnectionsWorkflow } from '../workflow/connections-workflow';
export { HyperbrowserHandler } from './handlers/HyperbrowserHandler';
