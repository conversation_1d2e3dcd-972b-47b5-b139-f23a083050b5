/**
 * Core form interfaces for HTMX form generation
 */

import { FormButton, FormField } from '../../agent/types';

export interface FormMetadata {
  errors: string[] | null;
  includedControls: IncludedControl[] | null;
}

export interface IncludedControl {
  id: string;
  status: 'included' | 'excluded';
  isExcludedReason:
    | 'Account Recovery'
    | 'Account Creation'
    | 'Password Visibility'
    | 'Profile Management'
    | 'Social Login'
    | 'Passkey Login'
    | 'Get Help'
    | 'Setup New Verification Methods'
    | 'None';
}

export interface FormControls {
  fields: FormField[];
  buttons: FormButton[];
}
