/**
 * Prompt display component
 */

/**
 * Generate prompt display markup for verification codes and contextual information
 */
export function generatePromptDisplay(
  instruction: string,
  screenCode: number | null,
  displayInstruction: boolean = true,
  standardLoginInstruction: string | null = null,
): string {
  if (!instruction && !screenCode) return '';

  const elements: string[] = [];

  const finalInstruction = standardLoginInstruction ? standardLoginInstruction : instruction;

  if (displayInstruction) {
    elements.push(`
      <div class="mb-2">${finalInstruction}</div>
    `);
  }

  if (screenCode) {
    elements.push(`
        <div class="verification-code-value">${screenCode}</div>
    `);
  }
  return `<div class="prompt-container">${elements.join('')}</div>`;
}
