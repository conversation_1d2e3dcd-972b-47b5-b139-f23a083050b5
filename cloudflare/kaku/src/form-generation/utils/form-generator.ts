/**
 * Isolated form generation utilities
 */

import { htmxFormGenerator } from '../htmx-generator';
import { ClassificationResult, ExtractionResult } from '../../agent/types/llm-result';

/**
 * Generate HTMX form markup from FormVisionResult
 * This is an isolated function that can be called independently
 */
export function generateHtmxForm(
  extractionResult: ExtractionResult,
  classificationResult: ClassificationResult,
): string {
  try {
    return htmxFormGenerator.generateForm(extractionResult, classificationResult);
  } catch (error) {
    console.error('Failed to generate HTMX form:', error);
    return 'Error generating form';
  }
}

/**
 * Determine if static description is required
 */
export function needsStaticDescription(extractionResult?: ExtractionResult): {
  shouldShowStaticDescription: boolean;
  staticCredentialText: string;
} {
  return { shouldShowStaticDescription: false, staticCredentialText: '' };
  // if (!extractionResult) {
  //   return { shouldShowStaticDescription: false, staticCredentialText: '' };
  // }

  // const keywords = ['password', 'email', 'username', 'phone number', 'phone'];
  //
  // const shouldShowStaticDescription = extractionResult.controls.fields.some((control) =>
  //   keywords.some((keyword) => control.label.toLowerCase().includes(keyword)),
  // );
  //
  // const staticCredentialText =
  //   extractionResult.controls.fields.length <= 1
  //     ? extractionResult.controls.fields[0].label.toLowerCase()
  //     : 'credentials';
  //
  // return { shouldShowStaticDescription, staticCredentialText };
}
