/**
 * Helper functions for making parallel LLM calls with different schemas
 */

import { LLMRequest } from './types/llm-request';
import {
  EXTRACTION_RESPONSE_SCHEMA,
  CLASSIFICATION_RESPONSE_SCHEMA,
} from './schemas/response-schemas';
import { LLMService } from './LLMService';
import { sanitizeJSONResponse } from '../workflow/utils/helpers';
import {
  ClassificationResult,
  ExtractionResult,
  PageStateResult,
  pageStateResultSchema,
} from '../agent/types/llm-result';

/**
 * Makes two parallel LLM calls:
 * 1. Extraction call - extracts form controls and metadata
 * 2. Classification call - provides screen classification and verification codes
 */
export async function makeParallelLLMCalls(
  llmService: LLMService,
  baseLLMRequest: Omit<LLMRequest, 'responseSchema' | 'prompt'>,
  extractionPrompt: string,
  classificationPrompt: string,
): Promise<PageStateResult> {
  // Create the two requests with different schemas
  const extractionRequest: LLMRequest = {
    ...baseLLMRequest,
    prompt: extractionPrompt,
    responseSchema: EXTRACTION_RESPONSE_SCHEMA,
  };

  const classificationRequest: LLMRequest = {
    ...baseLLMRequest,
    prompt: classificationPrompt,
    responseSchema: CLASSIFICATION_RESPONSE_SCHEMA,
  };

  // Make both calls in parallel
  const [extractionResponse, classificationResponse] = await Promise.all([
    llmService.raceLLMGetCalls({ llmRequest: extractionRequest, noOfConcurrentCalls: 2, enable: true }),
    llmService.raceLLMGetCalls({ llmRequest: classificationRequest, noOfConcurrentCalls: 2, enable: true }),
  ]);

  const extractionResult: ExtractionResult = sanitizeJSONResponse<ExtractionResult>(
    extractionResponse.output_text,
  );
  const classificationResult: ClassificationResult = sanitizeJSONResponse<ClassificationResult>(
    classificationResponse.output_text,
  );

  const result = {
    extractionResult,
    classificationResult,
  };

  try {
    return pageStateResultSchema.parse(result);
  } catch (error) {
    console.error('[kazeel][llm-parallel-calls] Validation failed:', error);
    console.error('[kazeel][llm-parallel-calls] Raw result:', result);
    throw new Error(
      `LLM response validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}
