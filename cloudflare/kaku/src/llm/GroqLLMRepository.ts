import Groq from 'groq-sdk';
import {
  generateCacheKeyForStateChangeLLMCalls,
  generateCacheKeyFromScreenshot,
} from '../common/utils';
import { LLMRepository } from './LLMRepository';
import { DetectPageStateChangeLlmRequest } from './types/detect-page-state-change-llm-request';
import { LLMResponse } from './types/llm-response';
import { LLMRequest } from './types/llm-request';
import { JSONSchemaObject } from 'openai/lib/jsonschema';

export class GroqLLMRepository implements LLMRepository {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL: string) {
    this.apiKey = apiKey;
    this.baseURL = baseURL;
  }

  async getLLMResponse(llmRequest: LLMRequest): Promise<LLMResponse> {
    const start = Date.now();
    const cacheKey = generateCacheKeyFromScreenshot(llmRequest, llmRequest.version);

    const client = new Groq({
      apiKey: this.apiKey,
      baseURL: this.baseURL,
      defaultHeaders: {
        'cf-aig-cache-key': cacheKey,
        'cf-aig-skip-cache': llmRequest.skipCache.toString(),
        'cf-aig-metadata': JSON.stringify({
          linkId: llmRequest.linkId,
        }),
      },
    });

    const completion = await client.chat.completions.create({
      model: 'meta-llama/llama-4-maverick-17b-128e-instruct',
      messages: [
        { role: 'system', content: llmRequest.prompt },
        {
          role: 'user',
          content: [
            {
              type: 'image_url',
              image_url: {
                url: `data:image/webp;base64,${llmRequest.screenshot}`,
                detail: 'low',
              },
            },
          ],
        },
      ],
      response_format: {
        type: 'json_schema',
        json_schema: {
          name: 'response',
          // strict: true,
          schema: llmRequest.responseSchema as JSONSchemaObject,
        },
      },
      temperature: 0,
      seed: 100,
    });

    const end = Date.now();
    const text = completion.choices?.[0]?.message?.content ?? '';
    if (!text) {
      throw new Error('Invalid response from Groq API');
    }

    return {
      output_text: text,
      callDuration: end - start,
    };
  }

  async detectStateChangeFromPreviousFormVisionResult(
    detectPageStateChangeLlmRequest: DetectPageStateChangeLlmRequest,
  ): Promise<LLMResponse> {
    const start = Date.now();
    const cacheKey = generateCacheKeyForStateChangeLLMCalls(detectPageStateChangeLlmRequest);

    const client = new Groq({
      apiKey: this.apiKey,
      baseURL: this.baseURL,
      defaultHeaders: {
        'cf-aig-cache-key': cacheKey,
        'cf-aig-skip-cache': detectPageStateChangeLlmRequest.skipCache.toString(),
        'cf-aig-metadata': JSON.stringify({
          linkId: detectPageStateChangeLlmRequest.linkId,
        }),
      },
    });

    const systemPrompt =
      detectPageStateChangeLlmRequest.prompt +
      JSON.stringify(detectPageStateChangeLlmRequest.agentVisionResultState);

    const completion = await client.chat.completions.create({
      model: 'meta-llama/llama-4-maverick-17b-128e-instruct',
      messages: [
        { role: 'system', content: systemPrompt },
        {
          role: 'user',
          content: [
            {
              type: 'image_url',
              image_url: {
                url: `data:image/webp;base64,${detectPageStateChangeLlmRequest.screenshot}`,
                detail: 'low',
              },
            },
          ],
        },
      ],
      temperature: 0,
      seed: 100,
    });

    const end = Date.now();
    const text = completion.choices?.[0]?.message?.content ?? '';
    if (!text) {
      throw new Error('Invalid response from Groq API');
    }

    return {
      output_text: text,
      callDuration: end - start,
    };
  }
}
