import { LLMResponse } from './types/llm-response';
import { LLMRepository } from './LLMRepository';
import { LLMRequest } from './types/llm-request';
import { DetectPageStateChangeLlmRequest } from './types/detect-page-state-change-llm-request';
import { DetectPageStateChangeResponse } from './types/detect-page-state-change-response';
import { sanitizeJSONResponse } from '../workflow/utils/helpers';

export class LLMService {
  private primaryRepo: LLMRepository;
  private secondaryRepo: LLMRepository;

  constructor({
    primaryRepo,
    secondaryRepo,
  }: {
    primaryRepo: LLMRepository;
    secondaryRepo: LLMRepository;
  }) {
    this.primaryRepo = primaryRepo;
    this.secondaryRepo = secondaryRepo;
  }

  async getLLMResponse(llmRequest: LLMRequest): Promise<LLMResponse> {
    try {
      return await this.primaryRepo.getLLMResponse(llmRequest);
    } catch (e) {
      //Try the secondary repository
      return await this.secondaryRepo.getLLMResponse(llmRequest);
    }
  }

  async raceLLMGetCalls(
      {
          llmRequest,
          noOfConcurrentCalls = 2,
          enable = true
      }:
      {
          llmRequest: LLMRequest,
          noOfConcurrentCalls: number,
          enable: boolean
      }
  ): Promise<LLMResponse> {
    try {
      const primaryLLMCalls = Array.from({ length: enable ? noOfConcurrentCalls : 1 }, (_, i) =>
        this.primaryRepo.getLLMResponse(llmRequest),
      );

      return await this.getResponseFromMultipleLLMCalls(primaryLLMCalls);
    } catch (e) {
      //Primary call failed, switch to the secondary repository
      const secondaryLLMCalls = Array.from({ length: enable ? noOfConcurrentCalls : 1 }, (_, i) =>
        this.secondaryRepo.getLLMResponse(llmRequest),
      );

      return await this.getResponseFromMultipleLLMCalls(secondaryLLMCalls);
    }
  }

  async detectStateChangeFromUserAgentState(
    detectPageStateChangeLlmRequest: DetectPageStateChangeLlmRequest,
  ): Promise<boolean> {
    const llmResponse = await (async () => {
      try {
        return await this.primaryRepo.detectStateChangeFromPreviousFormVisionResult(
          detectPageStateChangeLlmRequest,
        );
      } catch (e) {
        return await this.secondaryRepo.detectStateChangeFromPreviousFormVisionResult(
          detectPageStateChangeLlmRequest,
        );
      }
    })();

    return sanitizeJSONResponse<DetectPageStateChangeResponse>(llmResponse.output_text).hasChanges;
  }

  private error(...args: any[]): void {
    console.error('[kazeel][llm-service]', ...args);
  }
  private async getResponseFromMultipleLLMCalls(
    calls: Promise<LLMResponse>[],
  ): Promise<LLMResponse> {
    return new Promise((resolve, reject) => {
      let rejections: any[] = [];
      let pending = calls.length;

      calls.forEach((p) => {
        p.then(resolve).catch(async (err) => {
          rejections.push(err);
          pending--;
          if (pending === 0) reject(new AggregateError(rejections, 'All calls failed'));
        });
      });
    });
  }
}
