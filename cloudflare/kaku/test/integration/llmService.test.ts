import { describe, expect, it } from 'vitest';
import { LLMRepository } from '../../src/llm/LLMRepository';
import { LLMResponse } from '../../src/llm/types/llm-response';
import { LLMService } from '../../src/llm/LLMService';
import { LLMRequest } from '../../src/llm/types/llm-request';
import { EXTRACTION_RESPONSE_SCHEMA } from '../../src/llm/schemas/response-schemas';
import { DetectPageStateChangeLlmRequest } from '../../src/llm/types/detect-page-state-change-llm-request';

describe('LLMService Integration tests', () => {
    it('it should return an openai response on the get methods', async () => {
        //Arrange
        const primaryRepository = new MockLLMRepository();
        const secondaryRepository = new MockLLMRepository(true, true); //Shouldn't ever get to call this
        const llmService = new LLMService({
            primaryRepo: primaryRepository,
            secondaryRepo: secondaryRepository,
        });
        const llmRequest: LLMRequest = {
            platform: 'facebook',
            prompt: 'test prompt',
            screenshot: 'one',
            skipCache: false,
            viewportWidth: 1024,
            viewportHeight: 768,
            responseSchema: EXTRACTION_RESPONSE_SCHEMA,
        };

        //GetLLMResponse
        //Act
        await llmService.getLLMResponse(llmRequest);

        //Assert
        expect(primaryRepository.noOfGetCalls).toBe(1);
        expect(secondaryRepository.noOfGetCalls).toBe(0);

        //RaceLLMCalls
        //Arrange
        primaryRepository.noOfGetCalls = 0;
        secondaryRepository.noOfGetCalls = 0;
        //Act
        await llmService.raceLLMGetCalls({llmRequest, noOfConcurrentCalls: 2, enable: true});

        //Assert
        expect(primaryRepository.noOfGetCalls).toBe(2);
        expect(secondaryRepository.noOfGetCalls).toBe(0);
    });

    it('it should call the secondary repository in the case the first one fails', async () => {
        const primaryRepository = new MockLLMRepository(true, true); //Make it fail to force the secondaryRepository
        const secondaryRepository = new MockLLMRepository();
        const llmService = new LLMService({
            primaryRepo: primaryRepository,
            secondaryRepo: secondaryRepository,
        });

        const llmRequest: LLMRequest = {
            platform: 'facebook',
            prompt: 'test prompt',
            screenshot: 'one',
            skipCache: false,
            viewportWidth: 1024,
            viewportHeight: 768,
            responseSchema: EXTRACTION_RESPONSE_SCHEMA,
        };

        //GetLLMResponse
        //Act
        await llmService.getLLMResponse(llmRequest);

        //Assert
        expect(primaryRepository.noOfGetCalls).toBe(1);
        expect(secondaryRepository.noOfGetCalls).toBe(1);

        //RaceLLMCalls
        //Arrange
        primaryRepository.noOfGetCalls = 0;
        secondaryRepository.noOfGetCalls = 0;

        //Act
        await llmService.raceLLMGetCalls({llmRequest, noOfConcurrentCalls: 2, enable: true});

        //Assert
        expect(primaryRepository.noOfGetCalls).toBe(2);
        expect(secondaryRepository.noOfGetCalls).toBe(2);
    });

    it('it should not fail if one of the race calls fails', async () => {
        //Arrange
        const primaryRepository = new MockLLMRepository(false, true);
        const secondaryRepository = new MockLLMRepository(true, true);
        const llmService = new LLMService({
            primaryRepo: primaryRepository,
            secondaryRepo: secondaryRepository,
        });
        const llmRequest: LLMRequest = {
            platform: 'facebook',
            prompt: 'test prompt',
            screenshot: 'one',
            skipCache: false,
            viewportWidth: 1024,
            viewportHeight: 768,
            responseSchema: EXTRACTION_RESPONSE_SCHEMA,
        };

        //Act
        await llmService.raceLLMGetCalls({llmRequest, noOfConcurrentCalls: 2, enable: true});

        //Assert
        expect(primaryRepository.noOfGetCalls).toBe(2);
        expect(secondaryRepository.noOfGetCalls).toBe(0);
    });

    it('it should fail if all of the repository race calls fail', async () => {
        //Arrange
        const primaryRepository = new MockLLMRepository(true, true);
        const secondaryRepository = new MockLLMRepository(true, true);
        const llmService = new LLMService({
            primaryRepo: primaryRepository,
            secondaryRepo: secondaryRepository,
        });
        const llmRequest: LLMRequest = {
            platform: 'facebook',
            prompt: 'test prompt',
            screenshot: 'one',
            skipCache: false,
            viewportWidth: 1024,
            viewportHeight: 768,
            responseSchema: EXTRACTION_RESPONSE_SCHEMA,
        };

        //GetLLMResponse
        //Act
        let getFailed = false;
        try {
            await llmService.getLLMResponse(llmRequest);
            expect(true).toBe(false);
        } catch (e) {
            getFailed = true;
        }

        //Assert
        expect(getFailed).toBe(true);
        primaryRepository.noOfGetCalls = 1;
        secondaryRepository.noOfGetCalls = 1;

        //RaceLLMCalls
        //Arrange
        primaryRepository.noOfGetCalls = 0;
        secondaryRepository.noOfGetCalls = 0;
        //Act
        try {
            getFailed = false;
            await llmService.raceLLMGetCalls({llmRequest, noOfConcurrentCalls: 2, enable: true});
            expect(true).toBe(false);
        } catch (e) {
            getFailed = true;
        }

        //Assert
        expect(getFailed).toBe(true);
        expect(primaryRepository.noOfGetCalls).toBe(2);
        expect(secondaryRepository.noOfGetCalls).toBe(2);
    });
});

class MockLLMRepository implements LLMRepository {
    noOfGetCalls: number = 0;
    private shouldFailFirstCall: boolean;
    private shouldFailAllCalls: boolean;
    private shouldFailComparisonCall: boolean

    constructor(shouldFailAllCalls: boolean = false, shouldFailFirstCall: boolean = false, shouldFailComparisonCall: boolean = false) {
        this.shouldFailFirstCall = shouldFailFirstCall;
        this.shouldFailAllCalls = shouldFailAllCalls;
        this.shouldFailComparisonCall = shouldFailComparisonCall;
    }

    async detectStateChangeFromPreviousFormVisionResult(detectPageStateChangeLlmRequest: DetectPageStateChangeLlmRequest): Promise<LLMResponse> {
        if (this.shouldFailComparisonCall) {
            throw new Error('Forced comparison error');
        } else {
            return {
                callDuration: Date.now(),
                output_text: 'eight',
            };
        }
    }

  async getLLMResponse(llmRequest: LLMRequest): Promise<LLMResponse> {
    this.noOfGetCalls++;
    if (this.shouldFailAllCalls || (this.shouldFailFirstCall && this.noOfGetCalls === 1)) {
      throw Error(`Forced error on call no ${this.noOfGetCalls}`);
    } else {
      return {
        callDuration: Date.now(),
        output_text: 'seven',
      };
    }
  }
}
