import { env } from 'cloudflare:test';
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { CoordinatorDO } from '../../src/coordinator/coordinator-do';
import { PlatformTypes } from '../../src/ui/constants';
import { InternalHandler } from '../../src/api/handlers/InternalHandler';

describe.skip('InternalHandler Integration Tests', () => {
  let testCounter = 0;
  let validUsername: string;
  let validPassword: string;
  let invalidUsername: string;
  let invalidPassword: string;

  beforeEach(async () => {
    testCounter++;

    // Set up valid credentials from environment
    validUsername = env.CLOUDFLARE_INTEGRATION_USER_USERNAME || 'test-user';
    validPassword = env.CLOUDFLARE_INTEGRATION_USER_PASSWORD || 'test-password';

    // Set up invalid credentials for testing
    invalidUsername = 'invalid-user';
    invalidPassword = 'invalid-password';
  });

  afterEach(async () => {
    // Clean up any test data if needed
  });

  /**
   * Helper function to create basic auth header
   */
  const createBasicAuthHeader = (username: string, password: string): string => {
    return `Basic ${btoa(`${username}:${password}`)}`;
  };

  /**
   * Helper function to make authenticated request
   */
  const makeAuthenticatedRequest = async (
    path: string,
    method: string = 'GET',
    headers: Record<string, string> = {},
    body?: any,
  ) => {
    const authHeaders = {
      Authorization: createBasicAuthHeader(validUsername, validPassword),
      'Content-Type': 'application/json',
      ...headers,
    };

    const requestInit: RequestInit = {
      method,
      headers: authHeaders,
    };

    if (body) {
      requestInit.body = JSON.stringify(body);
    }

    return await InternalHandler.request(path, requestInit, env);
  };

  /**
   * Helper function to make unauthenticated request
   */
  const makeUnauthenticatedRequest = async (
    path: string,
    method: string = 'GET',
    headers: Record<string, string> = {},
    body?: any,
  ) => {
    const requestHeaders = {
      'Content-Type': 'application/json',
      ...headers,
    };

    const requestInit: RequestInit = {
      method,
      headers: requestHeaders,
    };

    if (body) {
      requestInit.body = JSON.stringify(body);
    }

    return await InternalHandler.request(path, requestInit, env);
  };

  /**
   * Helper function to make request with invalid credentials
   */
  const makeInvalidAuthRequest = async (
    path: string,
    method: string = 'GET',
    headers: Record<string, string> = {},
    body?: any,
  ) => {
    const authHeaders = {
      Authorization: createBasicAuthHeader(invalidUsername, invalidPassword),
      'Content-Type': 'application/json',
      ...headers,
    };

    const requestInit: RequestInit = {
      method,
      headers: authHeaders,
    };

    if (body) {
      requestInit.body = JSON.stringify(body);
    }

    return await InternalHandler.request(path, requestInit, env);
  };

  describe('Basic Authentication Middleware', () => {
    it('should allow access with valid credentials', async () => {
      // Arrange
      const userId = `test-user-auth-valid-${testCounter}`;

      // Act
      const response = await makeAuthenticatedRequest(`/links?userId=${userId}`);

      // Assert
      expect(response.status).toBe(200);
    });

    it('should deny access without authorization header', async () => {
      // Arrange
      const userId = `test-user-auth-none-${testCounter}`;

      // Act
      const response = await makeUnauthenticatedRequest(`/links?userId=${userId}`);

      // Assert
      expect(response.status).toBe(401);
      const responseData = (await response.json()) as any;
      expect(responseData.status).toBe('error');
      expect(responseData.message).toBe('Authentication failed');
    });

    it('should deny access with invalid credentials', async () => {
      // Arrange
      const userId = `test-user-auth-invalid-${testCounter}`;

      // Act
      const response = await makeInvalidAuthRequest(`/links?userId=${userId}`);

      // Assert
      expect(response.status).toBe(401);
      const responseData = (await response.json()) as any;
      expect(responseData.status).toBe('error');
      expect(responseData.message).toBe('Authentication failed');
    });

    it('should deny access with malformed authorization header', async () => {
      // Arrange
      const userId = `test-user-auth-malformed-${testCounter}`;

      // Act
      const response = await InternalHandler.request(
        `/links?userId=${userId}`,
        {
          method: 'GET',
          headers: {
            Authorization: 'InvalidFormat credentials',
            'Content-Type': 'application/json',
          },
        },
        env,
      );

      // Assert
      expect(response.status).toBe(401);
      const responseData = (await response.json()) as any;
      expect(responseData.status).toBe('error');
      expect(responseData.message).toBe('Authentication failed');
    });

    it('should deny access with empty credentials', async () => {
      // Arrange
      const userId = `test-user-auth-empty-${testCounter}`;

      // Act
      const response = await InternalHandler.request(
        `/links?userId=${userId}`,
        {
          method: 'GET',
          headers: {
            Authorization: createBasicAuthHeader('', ''),
            'Content-Type': 'application/json',
          },
        },
        env,
      );

      // Assert
      expect(response.status).toBe(401);
      const responseData = (await response.json()) as any;
      expect(responseData.status).toBe('error');
      expect(responseData.message).toBe('Authentication failed');
    });
  });

  describe('GET /links', () => {
    it('should return links for valid userId with authentication', async () => {
      // Arrange
      const userId = `test-user-links-${testCounter}`;

      // Act
      const response = await makeAuthenticatedRequest(`/links?userId=${userId}`);

      // Assert
      expect(response.status).toBe(200);
      const responseData = (await response.json()) as any;
      expect(responseData).toBeDefined();
    });

    it('should return 400 for missing userId parameter', async () => {
      // Act
      const response = await makeAuthenticatedRequest('/links');

      // Assert
      expect(response.status).toBe(400);
      const responseData = (await response.json()) as any;
      expect(responseData.error).toBe('Missing userId');
    });

    it('should return 400 for empty userId parameter', async () => {
      // Act
      const response = await makeAuthenticatedRequest('/links?userId=');

      // Assert
      expect(response.status).toBe(400);
      const responseData = (await response.json()) as any;
      expect(responseData.error).toBe('Missing userId');
    });

    it('should deny access without authentication', async () => {
      // Arrange
      const userId = `test-user-links-unauth-${testCounter}`;

      // Act
      const response = await makeUnauthenticatedRequest(`/links?userId=${userId}`);

      // Assert
      expect(response.status).toBe(401);
      const responseData = (await response.json()) as any;
      expect(responseData.status).toBe('error');
      expect(responseData.message).toBe('Authentication failed');
    });
  });

  describe('DELETE /connections', () => {
    it('should delete all connections with valid authentication and header', async () => {
      // Arrange
      const userId = `test-user-delete-${testCounter}`;

      // Act
      const response = await makeAuthenticatedRequest('/connections', 'DELETE', {
        'X-Impersonated-UserId': userId,
      });

      // Assert
      expect(response.status).toBe(200);
      const responseData = (await response.json()) as any;
      expect(responseData.success).toBe(true);
    });

    it('should return 400 for missing X-Impersonated-UserId header', async () => {
      // Act
      const response = await makeAuthenticatedRequest('/connections', 'DELETE');

      // Assert
      expect(response.status).toBe(400);
      const responseData = (await response.json()) as any;
      expect(responseData.error).toBe('Missing X-Impersonated-UserId header');
    });

    it('should deny access without authentication', async () => {
      // Arrange
      const userId = `test-user-delete-unauth-${testCounter}`;

      // Act
      const response = await makeUnauthenticatedRequest('/connections', 'DELETE', {
        'X-Impersonated-UserId': userId,
      });

      // Assert
      expect(response.status).toBe(401);
      const responseData = (await response.json()) as any;
      expect(responseData.status).toBe('error');
      expect(responseData.message).toBe('Authentication failed');
    });
  });

  describe('GET /:platformId/links', () => {
    const validPlatforms: PlatformTypes[] = ['facebook', 'github', 'google', 'netflix', 'kazeel'];

    validPlatforms.forEach((platformId) => {
      it(`should return platform links for ${platformId} with valid authentication`, async () => {
        // Arrange
        const userId = `test-user-platform-${platformId}-${testCounter}`;

        // Act
        const response = await makeAuthenticatedRequest(`/${platformId}/links`, 'GET', {
          'X-Impersonated-UserId': userId,
        });

        // Assert
        expect(response.status).toBe(200);
        const responseData = (await response.json()) as any;
        expect(responseData).toBeDefined();
      });
    });

    it('should return 400 for invalid platformId', async () => {
      // Arrange
      const userId = `test-user-invalid-platform-${testCounter}`;
      const invalidPlatformId = 'invalid-platform';

      // Act
      const response = await makeAuthenticatedRequest(`/${invalidPlatformId}/links`, 'GET', {
        'X-Impersonated-UserId': userId,
      });

      // Assert
      expect(response.status).toBe(400);
      const responseData = (await response.json()) as any;
      expect(responseData.error).toBe('Invalid platformId');
    });

    it('should return 400 for missing X-Impersonated-UserId header', async () => {
      // Act
      const response = await makeAuthenticatedRequest('/facebook/links');

      // Assert
      expect(response.status).toBe(400);
      const responseData = (await response.json()) as any;
      expect(responseData.error).toBe('Missing X-Impersonated-UserId header');
    });

    it('should deny access without authentication', async () => {
      // Arrange
      const userId = `test-user-platform-unauth-${testCounter}`;

      // Act
      const response = await makeUnauthenticatedRequest('/facebook/links', 'GET', {
        'X-Impersonated-UserId': userId,
      });

      // Assert
      expect(response.status).toBe(401);
      const responseData = (await response.json()) as any;
      expect(responseData.status).toBe('error');
      expect(responseData.message).toBe('Authentication failed');
    });

    it('should handle internal server errors gracefully', async () => {
      // Arrange
      const platformId = 'facebook';

      // Create a scenario that might cause an error by using a very long userId
      const veryLongUserId = 'a'.repeat(10000);

      // Act
      const response = await makeAuthenticatedRequest(`/${platformId}/links`, 'GET', {
        'X-Impersonated-UserId': veryLongUserId,
      });

      // Assert
      // Should either succeed or return a proper error response
      expect([200, 500].includes(response.status)).toBe(true);

      if (response.status === 500) {
        const responseData = (await response.json()) as any;
        expect(responseData.error).toBeDefined();
      }
    });
  });

  describe('POST /:platformId/links/:linkId/revoke', () => {
    const validPlatforms: PlatformTypes[] = ['facebook', 'github', 'google', 'netflix', 'kazeel'];

    validPlatforms.forEach((platformId) => {
      it(`should revoke link for ${platformId} with valid authentication`, async () => {
        // Arrange
        const userId = `test-user-revoke-${platformId}-${testCounter}`;

        // First create a link to revoke
        const coordinatorId = env.CoordinatorDO.idFromName(userId);
        const coordinator = env.CoordinatorDO.get(
          coordinatorId,
        ) as DurableObjectStub<CoordinatorDO>;
        const createResult = await coordinator.createLink(platformId, userId);

        // Act
        const response = await makeAuthenticatedRequest(
          `/${platformId}/links/${createResult.linkId}/revoke`,
          'POST',
          { 'X-Impersonated-UserId': userId },
        );

        // Assert
        expect(response.status).toBe(200);
        const responseData = (await response.json()) as any;
        expect(responseData.success).toBe(true);
        expect(responseData.message).toBe('Link revoked successfully');
      });
    });

    it('should return 404 for non-existent link', async () => {
      // Arrange
      const userId = `test-user-revoke-404-${testCounter}`;
      const platformId = 'facebook';
      const nonExistentLinkId = `non-existent-link-${testCounter}`;

      // Act
      const response = await makeAuthenticatedRequest(
        `/${platformId}/links/${nonExistentLinkId}/revoke`,
        'POST',
        { 'X-Impersonated-UserId': userId },
      );

      // Assert
      expect(response.status).toBe(404);
      const responseData = (await response.json()) as any;
      expect(responseData.error).toBe('Link not found or already revoked');
    });

    it('should return 400 for invalid platformId', async () => {
      // Arrange
      const userId = `test-user-revoke-invalid-platform-${testCounter}`;
      const invalidPlatformId = 'invalid-platform';
      const linkId = `test-link-${testCounter}`;

      // Act
      const response = await makeAuthenticatedRequest(
        `/${invalidPlatformId}/links/${linkId}/revoke`,
        'POST',
        { 'X-Impersonated-UserId': userId },
      );

      // Assert
      expect(response.status).toBe(400);
      const responseData = (await response.json()) as any;
      expect(responseData.error).toBe('Invalid platformId');
    });

    it('should return 400 for missing X-Impersonated-UserId header', async () => {
      // Arrange
      const platformId = 'facebook';
      const linkId = `test-link-${testCounter}`;

      // Act
      const response = await makeAuthenticatedRequest(
        `/${platformId}/links/${linkId}/revoke`,
        'POST',
      );

      // Assert
      expect(response.status).toBe(400);
      const responseData = (await response.json()) as any;
      expect(responseData.error).toBe('Missing X-Impersonated-UserId header');
    });

    it('should deny access without authentication', async () => {
      // Arrange
      const userId = `test-user-revoke-unauth-${testCounter}`;
      const platformId = 'facebook';
      const linkId = `test-link-${testCounter}`;

      // Act
      const response = await makeUnauthenticatedRequest(
        `/${platformId}/links/${linkId}/revoke`,
        'POST',
        { 'X-Impersonated-UserId': userId },
      );

      // Assert
      expect(response.status).toBe(401);
      const responseData = (await response.json()) as any;
      expect(responseData.status).toBe('error');
      expect(responseData.message).toBe('Authentication failed');
    });

    it('should handle internal server errors gracefully', async () => {
      // Arrange
      const platformId = 'facebook';
      const linkId = `test-link-${testCounter}`;

      // Create a scenario that might cause an error by using a very long userId
      const veryLongUserId = 'a'.repeat(10000);

      // Act
      const response = await makeAuthenticatedRequest(
        `/${platformId}/links/${linkId}/revoke`,
        'POST',
        { 'X-Impersonated-UserId': veryLongUserId },
      );

      // Assert
      // Should either succeed or return a proper error response
      expect([200, 404, 500].includes(response.status)).toBe(true);

      if (response.status === 500) {
        const responseData = (await response.json()) as any;
        expect(responseData.error).toBeDefined();
      }
    });

    it('should handle already revoked links', async () => {
      // Arrange
      const userId = `test-user-revoke-twice-${testCounter}`;
      const platformId = 'facebook';

      // First create a link
      const coordinatorId = env.CoordinatorDO.idFromName(userId);
      const coordinator = env.CoordinatorDO.get(coordinatorId) as DurableObjectStub<CoordinatorDO>;
      const createResult = await coordinator.createLink(platformId, userId);

      // Revoke it once
      const firstRevokeResponse = await makeAuthenticatedRequest(
        `/${platformId}/links/${createResult.linkId}/revoke`,
        'POST',
        { 'X-Impersonated-UserId': userId },
      );
      expect(firstRevokeResponse.status).toBe(200);

      // Act - Try to revoke again
      const secondRevokeResponse = await makeAuthenticatedRequest(
        `/${platformId}/links/${createResult.linkId}/revoke`,
        'POST',
        { 'X-Impersonated-UserId': userId },
      );

      // Assert
      expect(secondRevokeResponse.status).toBe(404);
      const responseData = (await secondRevokeResponse.json()) as any;
      expect(responseData.error).toBe('Link not found or already revoked');
    });
  });

  describe('Integration Tests with Real CoordinatorDO', () => {
    it('should create, retrieve, and revoke links end-to-end', async () => {
      // Arrange
      const userId = `test-user-e2e-${testCounter}`;
      const platformId: PlatformTypes = 'github';

      // Create a link directly via CoordinatorDO
      const coordinatorId = env.CoordinatorDO.idFromName(userId);
      const coordinator = env.CoordinatorDO.get(coordinatorId) as DurableObjectStub<CoordinatorDO>;
      const createResult = await coordinator.createLink(platformId, userId);

      // Act & Assert - Get all links
      const linksResponse = await makeAuthenticatedRequest(`/links?userId=${userId}`);
      expect(linksResponse.status).toBe(200);

      // Act & Assert - Get platform-specific links
      const platformLinksResponse = await makeAuthenticatedRequest(`/${platformId}/links`, 'GET', {
        'X-Impersonated-UserId': userId,
      });
      expect(platformLinksResponse.status).toBe(200);

      // Act & Assert - Revoke the link
      const revokeResponse = await makeAuthenticatedRequest(
        `/${platformId}/links/${createResult.linkId}/revoke`,
        'POST',
        { 'X-Impersonated-UserId': userId },
      );
      expect(revokeResponse.status).toBe(200);
      const revokeData = (await revokeResponse.json()) as any;
      expect(revokeData.success).toBe(true);

      // Act & Assert - Delete all connections
      const deleteResponse = await makeAuthenticatedRequest('/connections', 'DELETE', {
        'X-Impersonated-UserId': userId,
      });
      expect(deleteResponse.status).toBe(200);
      const deleteData = (await deleteResponse.json()) as any;
      expect(deleteData.success).toBe(true);
    });
  });
});
