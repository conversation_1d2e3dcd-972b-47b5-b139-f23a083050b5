import { describe, expect, it, beforeEach, afterEach, vi } from 'vitest';
import { env, fetchMock } from 'cloudflare:test';
import { createSunnyClient, SunnyClient } from '../../src/api/clients';
import { LocalSunnyClient } from '../../src/api/clients/LocalSunnyClient';
import { ProdSunnyClient } from '../../src/api/clients/ProdSunnyClient';

describe('SunnyClient', () => {
  const TEST_USER_ID = 'testUserId';
  const TEST_CONNECTION_ID = 'testConnectionId';
  const TEST_PLATFORM = 'testPlatform';
  const SUNNY_API_ENDPOINT = 'http://localhost:8080';
  const TEST_USERNAME = 'testuser';
  const TEST_PASSWORD = 'testpassword';
  const BASIC_AUTH_HEADER = `Basic ${btoa(`${TEST_USERNAME}:${TEST_PASSWORD}`)}`;

  beforeEach(() => {
    fetchMock.activate();
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});

    // Test environment variables
    env.SUNNY_API_ENDPOINT = SUNNY_API_ENDPOINT;
    env.CLOUDFLARE_INTEGRATION_USER_USERNAME = TEST_USERNAME;
    env.CLOUDFLARE_INTEGRATION_USER_PASSWORD = TEST_PASSWORD;
  });

  afterEach(() => {
    fetchMock.deactivate();
    vi.restoreAllMocks();
  });

  describe('LocalSunnyClient', () => {
    beforeEach(() => {
      env.ENVIRONMENT = 'local';
    });

    it('should return LocalSunnyClient in local environment', () => {
      const client = createSunnyClient(env);
      expect(client).toBeInstanceOf(LocalSunnyClient);
    });

    it('should log event and not make a fetch call', async () => {
      const client = createSunnyClient(env);
      const consoleSpy = vi.spyOn(console, 'log');
      const fetchSpy = vi.spyOn(global, 'fetch');

      await client.sendConnectionEvent({
        userId: TEST_USER_ID,
        connectionStatus: 'connected',
        connectionId: TEST_CONNECTION_ID,
        platform: TEST_PLATFORM,
      });

      expect(consoleSpy).toHaveBeenCalledWith(
        '[LocalSunnyClient] Simulating connection event:',
        expect.objectContaining({
          userId: TEST_USER_ID,
          connectionStatus: 'connected',
          connectionId: TEST_CONNECTION_ID,
          platform: TEST_PLATFORM,
        }),
      );
      expect(fetchSpy).not.toHaveBeenCalled();
    });
  });

  describe('ProdSunnyClient', () => {
    beforeEach(() => {
      env.ENVIRONMENT = 'prod';
    });

    it('should return ProdSunnyClient in prod environment', () => {
      const client = createSunnyClient(env);
      expect(client).toBeInstanceOf(ProdSunnyClient);
    });

    it('should send connection event successfully', async () => {
      const client = createSunnyClient(env);
      const fetchSpy = vi.spyOn(global, 'fetch');

      fetchMock
        .get(env.SUNNY_API_ENDPOINT)
        .intercept({
          path: '/v1/internal/connections/event',
          headers: {
            'Content-Type': 'application/json',
            'X-Impersonated-UserId': TEST_USER_ID,
            'Authorization': BASIC_AUTH_HEADER,
          },
          method: 'POST',
          body: JSON.stringify({
            userId: TEST_USER_ID,
            connectionStatus: 'connected',
            connectionId: TEST_CONNECTION_ID,
            platform: TEST_PLATFORM,
          }),
        })
        .reply(200, {});

      await expect(client.sendConnectionEvent({
        userId: TEST_USER_ID,
        connectionStatus: 'connected',
        connectionId: TEST_CONNECTION_ID,
        platform: TEST_PLATFORM,
      })).resolves.not.toThrow();
      expect(fetchSpy).toHaveBeenCalledTimes(1);
    });

    it('should throw an error on failed connection event', async () => {
      const client = createSunnyClient(env);
      const errorMessage = 'Internal Server Error';
      const fetchSpy = vi.spyOn(global, 'fetch');

      fetchMock
        .get(env.SUNNY_API_ENDPOINT)
        .intercept({
          path: '/v1/internal/connections/event',
          headers: {
            'Content-Type': 'application/json',
            'X-Impersonated-UserId': TEST_USER_ID,
            'Authorization': BASIC_AUTH_HEADER,
          },
          method: 'POST',
          body: JSON.stringify({
            userId: TEST_USER_ID,
            connectionStatus: 'connected',
            connectionId: TEST_CONNECTION_ID,
            platform: TEST_PLATFORM,
          }),
        })
        .reply(500, errorMessage);

      await expect(client.sendConnectionEvent({
        userId: TEST_USER_ID,
        connectionStatus: 'connected',
        connectionId: TEST_CONNECTION_ID,
        platform: TEST_PLATFORM,
      })).rejects.toThrow(
        `Failed to send connection event to Sunny API: 500 Internal Server Error - ${errorMessage}`,
      );
      expect(fetchSpy).toHaveBeenCalledTimes(1);
    });
  });

  describe('createSunnyClient with dev environment', () => {
    beforeEach(() => {
      env.ENVIRONMENT = 'dev';
    });

    it.skip('should return LocalSunnyClient in dev environment', () => { // TODO: remove this SKIP test after testing integration with Sunny
      const client = createSunnyClient(env);
      expect(client).toBeInstanceOf(LocalSunnyClient);
    });

    it.skip('should log event and not make a fetch call in dev environment', async () => { // TODO: remove this SKIP test after testing integration with Sunny
      const client = createSunnyClient(env);
      const consoleSpy = vi.spyOn(console, 'log');
      const fetchSpy = vi.spyOn(global, 'fetch');

      await client.sendConnectionEvent({
        userId: TEST_USER_ID,
        connectionStatus: 'connected',
        connectionId: TEST_CONNECTION_ID,
        platform: TEST_PLATFORM,
      });
      expect(consoleSpy).toHaveBeenCalledWith(
        '[LocalSunnyClient] Simulating connection event:',
        expect.objectContaining({
          userId: TEST_USER_ID,
          connectionStatus: 'connected',
          connectionId: TEST_CONNECTION_ID,
          platform: TEST_PLATFORM,
        }),
      );
      expect(fetchSpy).not.toHaveBeenCalled();
    });
  });
});
