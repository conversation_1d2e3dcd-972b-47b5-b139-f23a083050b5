import { describe, it, expect, beforeEach } from 'vitest';
import {
  getEncryptionKey,
  encryptData,
  decryptData,
  arrayBufferToBase64,
  base64ToArrayBuffer,
} from '../../../src/common/utils/index';

const encryptionKeyString = 'your-32-byte-secret-key-12345678';

describe('Encryption Utils', () => {
  let encryptionKey: CryptoKey;

  beforeEach(async () => {
    encryptionKey = await getEncryptionKey(encryptionKeyString);
  });

  describe('getEncryptionKey', () => {
    it('should generate a valid AES-GCM key', async () => {
      const key = await getEncryptionKey(encryptionKeyString);

      expect(key).toBeInstanceOf(CryptoKey);
      expect(key.algorithm.name).toBe('AES-GCM');
      expect(key.type).toBe('secret');
      expect(key.usages).toContain('encrypt');
      expect(key.usages).toContain('decrypt');
    });

    it('should generate the same key consistently', async () => {
      const key1 = await getEncryptionKey(encryptionKeyString);
      const key2 = await getEncryptionKey(encryptionKeyString);

      // Keys should have the same properties (we can't directly compare CryptoKey objects)
      expect(key1.algorithm.name).toBe(key2.algorithm.name);
      expect(key1.type).toBe(key2.type);
      expect(key1.usages).toEqual(key2.usages);
    });
  });

  describe('encryptData and decryptData', () => {
    it('should encrypt and decrypt a simple string', async () => {
      const originalData = 'Hello, World!';

      const encrypted = await encryptData(originalData, encryptionKey);
      const decrypted = await decryptData(encrypted, encryptionKey);

      expect(decrypted).toBe(originalData);
      expect(encrypted).toMatch(/^[A-Za-z0-9+/]+=*$/); // Base64 pattern
    });

    it('should encrypt and decrypt a number', async () => {
      const originalData = 42;

      const encrypted = await encryptData(originalData, encryptionKey);
      const decrypted = await decryptData(encrypted, encryptionKey);

      expect(decrypted).toBe(originalData);
    });

    it('should encrypt and decrypt a boolean', async () => {
      const originalData = true;

      const encrypted = await encryptData(originalData, encryptionKey);
      const decrypted = await decryptData(encrypted, encryptionKey);

      expect(decrypted).toBe(originalData);
    });

    it('should encrypt and decrypt an object', async () => {
      const originalData = {
        name: 'John Doe',
        age: 30,
        active: true,
        metadata: {
          lastLogin: '2023-01-01',
          permissions: ['read', 'write'],
        },
      };

      const encrypted = await encryptData(originalData, encryptionKey);
      const decrypted = await decryptData(encrypted, encryptionKey);

      expect(decrypted).toEqual(originalData);
    });

    it('should encrypt and decrypt an array', async () => {
      const originalData = [1, 'two', { three: 3 }, [4, 5]];

      const encrypted = await encryptData(originalData, encryptionKey);
      const decrypted = await decryptData(encrypted, encryptionKey);

      expect(decrypted).toEqual(originalData);
    });

    it('should encrypt and decrypt null', async () => {
      const originalData = null;

      const encrypted = await encryptData(originalData, encryptionKey);
      const decrypted = await decryptData(encrypted, encryptionKey);

      expect(decrypted).toBe(originalData);
    });

    it('should produce different encrypted outputs for the same input', async () => {
      const originalData = 'Same input';

      const encrypted1 = await encryptData(originalData, encryptionKey);
      const encrypted2 = await encryptData(originalData, encryptionKey);

      // Should be different due to random IV
      expect(encrypted1).not.toBe(encrypted2);

      // But both should decrypt to the same value
      const decrypted1 = await decryptData(encrypted1, encryptionKey);
      const decrypted2 = await decryptData(encrypted2, encryptionKey);

      expect(decrypted1).toBe(originalData);
      expect(decrypted2).toBe(originalData);
    });

    it('should handle empty string', async () => {
      const originalData = '';

      const encrypted = await encryptData(originalData, encryptionKey);
      const decrypted = await decryptData(encrypted, encryptionKey);

      expect(decrypted).toBe(originalData);
    });

    it('should handle large data', async () => {
      const originalData = 'x'.repeat(10000); // 10KB string

      const encrypted = await encryptData(originalData, encryptionKey);
      const decrypted = await decryptData(encrypted, encryptionKey);

      expect(decrypted).toBe(originalData);
    });

    it('should fail to decrypt with wrong key', async () => {
      const originalData = 'Secret data';
      const wrongKey = await crypto.subtle.importKey(
        'raw',
        new TextEncoder().encode('different-32-byte-key-1234567890'),
        { name: 'AES-GCM' },
        false,
        ['encrypt', 'decrypt'],
      );

      const encrypted = await encryptData(originalData, encryptionKey);

      await expect(decryptData(encrypted, wrongKey)).rejects.toThrow();
    });

    it('should fail to decrypt corrupted data', async () => {
      const originalData = 'Test data';
      const encrypted = await encryptData(originalData, encryptionKey);

      // Corrupt the encrypted data
      const corruptedData = encrypted.slice(0, -5) + 'XXXXX';

      await expect(decryptData(corruptedData, encryptionKey)).rejects.toThrow();
    });
  });

  describe('decryptDataWithDefaultKey', () => {
    it('should decrypt data using the default key', async () => {
      const originalData = { message: 'Hello from convenience function!' };

      // Encrypt with default key
      const defaultKey = await getEncryptionKey(encryptionKeyString);
      const encrypted = await encryptData(originalData, defaultKey);

      // Decrypt using convenience function

      const decrypted = await decryptData(encrypted, defaultKey);

      expect(decrypted).toEqual(originalData);
    });

    it('should work with various data types', async () => {
      const testCases = ['string data', 123, true, { key: 'value' }, [1, 2, 3], null];

      for (const originalData of testCases) {
        const defaultKey = await getEncryptionKey(encryptionKeyString);
        const encrypted = await encryptData(originalData, defaultKey);
        const decrypted = await decryptData(encrypted, defaultKey);

        expect(decrypted).toEqual(originalData);
      }
    });
  });

  describe('arrayBufferToBase64 and base64ToArrayBuffer', () => {
    it('should convert ArrayBuffer to base64 and back', () => {
      const originalData = new Uint8Array([1, 2, 3, 4, 5, 255, 0, 128]);
      const arrayBuffer = originalData.buffer;

      const base64 = arrayBufferToBase64(arrayBuffer);
      const convertedBack = base64ToArrayBuffer(base64);
      const resultArray = new Uint8Array(convertedBack);

      expect(resultArray).toEqual(originalData);
      expect(base64).toMatch(/^[A-Za-z0-9+/]+=*$/); // Base64 pattern
    });

    it('should handle empty ArrayBuffer', () => {
      const emptyBuffer = new ArrayBuffer(0);

      const base64 = arrayBufferToBase64(emptyBuffer);
      const convertedBack = base64ToArrayBuffer(base64);

      expect(convertedBack.byteLength).toBe(0);
    });

    it('should be consistent with multiple conversions', () => {
      const testData = new Uint8Array(Array.from({ length: 256 }, (_, i) => i));
      const arrayBuffer = testData.buffer;

      const base64_1 = arrayBufferToBase64(arrayBuffer);
      const back_1 = base64ToArrayBuffer(base64_1);
      const base64_2 = arrayBufferToBase64(back_1);
      const back_2 = base64ToArrayBuffer(base64_2);

      expect(base64_1).toBe(base64_2);
      expect(new Uint8Array(back_1)).toEqual(new Uint8Array(back_2));
      expect(new Uint8Array(back_2)).toEqual(testData);
    });
  });
});
