/**
 * Test for PageStateResult Zod schema validation
 */

import { describe, expect, it } from 'vitest';
import { pageStateResultSchema } from '../../src/agent/types/llm-result';

describe('PageStateResult Schema Validation', () => {
  it('should validate a valid PageStateResult', () => {
    const validPageStateResult = {
      extractionResult: {
        screenInfo: {
          errors: null,
          controlReasoning: [
            {
              id: 'email',
              status: 'included',
              reasoning: 'Email input field is required for login',
              isControlEnabledReasoning: 'Control is enabled and visible',
            },
          ],
        },
        includedControls: {
          fields: [
            {
              id: 'email',
              order: 1,
              label: 'Email',
              isLikelyDropdownReason: 'No dropdown arrow or chevron icon visible',
              isLikelyDropdown: false,
              fieldControlType: 'text' as const,
              actiontype: 'fill' as const,
              name: 'email',
              checked: false,
              isDontAskAgainControl: false,
              options: null,
            },
          ],
          buttons: [
            {
              id: 'submit',
              order: 2,
              label: 'Sign In',
              variant: 'primary' as const,
              type: 'submit' as const,
              actiontype: 'click' as const,
              isDontAskAgainControl: false,
              requiresThirdPartyPlatform: false,
            },
          ],
        },
      },
      classificationResult: {
        screenInfo: {
          title: 'Login Screen',
          classificationReasoning: 'This is a login form with email and password fields',
          authStateReasoning: 'User is not logged in as evidenced by login form',
          screenClass: 'other' as const,
          targetPlatform: 'TestPlatform',
          instruction: 'Enter your credentials',
          standardLoginInstruction: 'Enter your credentials',
          authState: 'not-authenticated' as const,
          screenCodeReasoning: 'No verification code visible on screen',
          screenCode: null,
          alertsCount: 0,
          alertsAnalysis: 'No alerts detected',
          alerts: [],
          alertsSummary: 'No issues found',
        },
      },
    };

    expect(() => pageStateResultSchema.parse(validPageStateResult)).not.toThrow();
  });

  it('should reject invalid extractionResult', () => {
    const invalidPageStateResult = {
      extractionResult: {
        screenInfo: {
          errors: null,
          controlReasoning: null,
        },
        includedControls: {
          fields: [
            {
              id: 'email',
              order: 1,
              label: 'Email',
              isLikelyDropdownReason: 'No dropdown visible',
              isLikelyDropdown: false,
              fieldControlType: 'invalid-type', // Invalid field type
              actiontype: 'fill',
              name: 'email',
              checked: false,
              isDontAskAgainControl: false,
            },
          ],
          buttons: [],
        },
      },
      classificationResult: {
        screenInfo: {
          title: 'Login Screen',
          classificationReasoning: 'This is a login form',
          authStateReasoning: 'User not authenticated',
          screenClass: 'other',
          targetPlatform: 'TestPlatform',
          instruction: 'Enter your credentials',
          standardLoginInstruction: 'Enter your credentials',
          authState: 'not-authenticated',
          screenCodeReasoning: 'No code visible',
          screenCode: null,
          alertsCount: 0,
          alertsAnalysis: 'No alerts detected',
          alerts: [],
          alertsSummary: 'No issues found',
        },
      },
    };

    expect(() => pageStateResultSchema.parse(invalidPageStateResult)).toThrow();
  });

  it('should reject invalid classificationResult', () => {
    const invalidPageStateResult = {
      extractionResult: {
        screenInfo: {
          errors: null,
          controlReasoning: null,
        },
        includedControls: {
          fields: [],
          buttons: [],
        },
      },
      classificationResult: {
        screenInfo: {
          title: 'Login Screen',
          classificationReasoning: 'This is a login form',
          authStateReasoning: 'User not authenticated',
          screenClass: 'other',
          targetPlatform: 'TestPlatform',
          instruction: 'Enter your credentials',
          standardLoginInstruction: 'Enter your credentials',
          authState: 'invalid-auth-state', // Invalid auth state
          screenCodeReasoning: 'No code visible',
          screenCode: null,
          alertsCount: 0,
          alertsAnalysis: 'No alerts detected',
          alerts: [],
          alertsSummary: 'No issues found',
        },
      },
    };

    expect(() => pageStateResultSchema.parse(invalidPageStateResult)).toThrow();
  });

  it('should reject missing required fields', () => {
    const incompletePageStateResult = {
      extractionResult: {
        screenInfo: {
          errors: null,
          controlReasoning: null,
        },
        includedControls: {
          fields: [],
          buttons: [],
        },
      },
      // Missing classificationResult
    };

    expect(() => pageStateResultSchema.parse(incompletePageStateResult)).toThrow();
  });

  it('should validate alerts array correctly', () => {
    const pageStateResultWithAlerts = {
      extractionResult: {
        screenInfo: {
          errors: ['Invalid credentials'],
          controlReasoning: null,
        },
        includedControls: {
          fields: [],
          buttons: [],
        },
      },
      classificationResult: {
        screenInfo: {
          title: 'Login Error',
          classificationReasoning: 'Login failed due to invalid credentials',
          authStateReasoning: 'User authentication failed',
          screenClass: 'other' as const,
          targetPlatform: 'TestPlatform',
          instruction: 'Please check your credentials',
          standardLoginInstruction: 'Please check your credentials',
          authState: 'not-authenticated' as const,
          screenCodeReasoning: 'No verification code present',
          screenCode: null,
          alertsCount: 1,
          alertsAnalysis: 'Error detected',
          alerts: [
            {
              alertType: 'error' as const,
              alertText: 'Invalid username or password',
            },
          ],
          alertsSummary: 'Login failed due to invalid credentials',
        },
      },
    };

    expect(() => pageStateResultSchema.parse(pageStateResultWithAlerts)).not.toThrow();
    const result = pageStateResultSchema.parse(pageStateResultWithAlerts);
    expect(result.classificationResult.screenInfo.alerts).toHaveLength(1);
    expect(result.classificationResult.screenInfo.alerts?.[0].alertType).toBe('error');
  });
});
