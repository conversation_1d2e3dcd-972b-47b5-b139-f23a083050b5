# Browser Flags Documentation

## Overview

This document outlines the custom browser flags (Chrome arguments) used in Kaku for browser automation. These flags are essential for enabling automated screen capture and interaction capabilities in headless browser environments.

## Current Browser Flags

### `--auto-accept-this-tab-capture`

**Purpose**: Automatically accepts tab capture permissions without user interaction.

**Why it's needed**:

- <PERSON><PERSON> operates in an automated browser environment where no real user is present to manually select tabs for capture
- The `getDisplayMedia()` API normally requires user interaction to choose which screen/tab to capture
- This flag bypasses the browser's tab selection dialog, automatically granting permission to capture the current tab

**Impact on Architecture**:

- Forces injection of `getDisplayMedia` scripts into the same tab being captured
- Eliminates the need for cross-tab permission handling
- Enables seamless screen streaming for captcha detection and visual monitoring

## Reference

The given link provides a comprehensive list of all available Chrome flags:
https://peter.sh/experiments/chromium-command-line-switches/
